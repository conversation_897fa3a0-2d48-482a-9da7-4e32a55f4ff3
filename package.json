{"name": "edu_management", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"echarts": "^5.6.0", "pinia": "^3.0.2", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@ricons/antd": "^0.13.0", "@ricons/carbon": "^0.13.0", "@ricons/fluent": "^0.13.0", "@ricons/ionicons4": "^0.13.0", "@ricons/ionicons5": "^0.13.0", "@ricons/material": "^0.13.0", "@ricons/tabler": "^0.13.0", "@tauri-apps/api": "^2.5.0", "@tauri-apps/cli": "^2.5.0", "@vicons/antd": "^0.13.0", "@vicons/carbon": "^0.13.0", "@vicons/fluent": "^0.13.0", "@vicons/ionicons4": "^0.13.0", "@vicons/ionicons5": "^0.13.0", "@vicons/material": "^0.13.0", "@vicons/tabler": "^0.13.0", "@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.21", "naive-ui": "^2.41.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}