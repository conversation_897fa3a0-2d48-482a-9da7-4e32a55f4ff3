{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "授课管理系统", "version": "0.1.0", "identifier": "com.edumanagement.dev", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:5173", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "授课系统-演示", "width": 1200, "height": 768, "resizable": true, "fullscreen": false, "url": "index.html"}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}