
<template>
  <n-config-provider  preflight-style-disabled :locale="zhCN">
    <n-message-provider>
      <n-dialog-provider>

        <n-layout position="absolute">
          <n-layout-header bordered>
            <Header />
          </n-layout-header>
          <n-layout-content position="absolute" style="top: 48px;" class="bg-gray-50">
            <router-view />


          </n-layout-content>
        </n-layout>
      </n-dialog-provider>

    </n-message-provider>
  </n-config-provider>
</template>

<script lang="ts" setup>
import { zhCN } from 'naive-ui';
import {Header} from "@/components";

</script>
