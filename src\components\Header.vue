<template>
    <div class="h-12 flex items-center border-b-1 border-gray-300 hover:bg-gray-200">
        <div class=" mx-8 flex items-center cursor-pointer w-80" @click="active_button_id=1">
            <div class="font-bold text-gray-800 text-2xl mx-2">
                干部授课管理系统
            </div>
        </div>
        <div class="ml-24 flex items-center w-full">
            <div class="flex">
                <div v-for="item in button_list" :key="item.id" class="mx-1">
                    <n-button quaternary :type="item.id === active_button_id ? 'primary' : 'default'"   @click="handle_click(item.id)">
                        {{ item.text }}
                    </n-button>
                </div>
            </div>
            <!-- 使用 flex-grow 和 flex-shrink 来推开右侧按钮 -->
           
        </div>
    </div>
</template>
<script lang="js" setup>
import {ref} from 'vue';
import {useRouter} from 'vue-router';
import {ArrowUndo,LogInOutline} from '@vicons/ionicons5';
const router = useRouter();
const routerTo = (url) => {
    router.push(url)
}
const active_button_id = ref(1);
const button_list = ref([
    {id: 1, text: '总览'},
    {id: 2, text: '干部管理'},
    {id: 3, text: '中央单位管理'},
    {id: 4, text: '高校管理'},
    {id: 5, text: '授课计划管理'},
]);


const handle_click = (id) => {
    active_button_id.value = id;
  switch (id) {
        case 1:
            routerTo('/');
            break;
        case 2:
            routerTo('/cadres')
            break;
        case 3:
            routerTo('/unit')
            break;
        case 4:
            routerTo('/universities')
            break;
        case 5:
            routerTo('/plan')
            break;   
        default:
            console.log('未定义的id');
            break;
    }
}
</script>