import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
//   {
//     path: '/login',
//     name: 'Login',
//     component: () => import('@/views/login/index.vue')
//   },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/dashboard/index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/cadres',
    name: 'Cadres',
    component: () => import('@/views/cadres/index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/plan',
    name: 'Plan',
    component: () => import('@/views/plan/index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/unit',
    name: 'Unit',
    component: () => import('@/views/unit/index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/universities',
    name: 'Universities',
    component: () => import('@/views/universities/index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/404.vue')
  }
]

const router = createRouter({
 history: createWebHashHistory(), 
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
//   const isAuthenticated = localStorage.getItem('token')
const isAuthenticated = true
  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login')
  } else {
    next()
  }
})

export default router