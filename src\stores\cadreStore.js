// src/stores/cadreStore.js
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { teachingDirectionOptions } from '@/views/cadres/cadreOptions'; // 路径可能需要调整
// 不再从 cadreOptions 导入 universityOptions，而是从 universityStore 获取
// import { universityOptions } from '@/views/cadres/cadreOptions';
import { useUnitStore } from './unitStore'; // 导入 unitStore
import { useUniversityStore } from './universityStore'; // 导入 universityStore

const createInitialData = () => {
  const initialData = [];
  const names = ['张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十', '陈十一', '杨十二', '朱十三', '秦十四', '许十五'];
  const genders = ['male', 'female'];
  const nativePlaces = ['北京', '上海', '广州', '深圳', '成都', '杭州', '武汉', '西安'];
  const levels = ['provincial', 'departmental', 'county', 'township'];
  const positions = ['处长', '副处长', '科长', '科员', '主任', '副主任'];
  const cadreTypes = ['party_government', 'professional_technical'];
  const educations = ['doctorate', 'master', 'bachelor', 'associate'];
  const degrees = ['phd', 'master_degree', 'bachelor_degree', 'none'];

  const teachingDirsValues = teachingDirectionOptions.map(o => o.value);

  // 获取真实的单位ID和高校ID列表用于模拟数据
  // 注意：这里假设 unitStore 和 universityStore 已经有数据。
  // 在实际应用初始化时，你可能需要确保这些 store 的数据已加载。
  // 为了简化，我们这里假设它们在 createInitialData 调用时已有 mock 数据。
  const unitStore = useUnitStore();
  const universityStore = useUniversityStore();

  // 如果 store 是在 setup 外立即执行，stores 可能还未初始化。
  // 实际应用中，你可能在 fetchCadres 内部获取这些依赖数据
  const availableUnitIds = unitStore.allUnits.length > 0 ? unitStore.allUnits.map(u => u.id) : [1, 2, 3]; // 示例ID
  const availableUniversityIds = universityStore.rawUniversities.length > 0 ? universityStore.rawUniversities.map(u => u.id) : [1, 2, 3]; // 示例ID


  for (let i = 1; i <= 45; i++) {
    initialData.push({
      id: `id-<span class="math-inline">\{Date\.now\(\)\}\-</span>{i}-${Math.random().toString(36).substring(2, 9)}`,
      name: names[Math.floor(Math.random() * names.length)],
      gender: genders[Math.floor(Math.random() * genders.length)],
      birthDate: new Date(1960 + Math.floor(Math.random() * 35), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).getTime(),
      nativePlace: nativePlaces[Math.floor(Math.random() * nativePlaces.length)],
      level: levels[Math.floor(Math.random() * levels.length)],
      position: positions[Math.floor(Math.random() * positions.length)],
      cadreType: cadreTypes[Math.floor(Math.random() * cadreTypes.length)],
      // 修改这里：存储单位ID
      unitId: availableUnitIds.length > 0 ? availableUnitIds[Math.floor(Math.random() * availableUnitIds.length)] : null,
      // unitAffiliation: units[Math.floor(Math.random() * units.length)], // 旧的方式
      highestEducation: educations[Math.floor(Math.random() * educations.length)],
      highestDegree: degrees[Math.floor(Math.random() * degrees.length)],
      expertise: `熟悉领域<span class="math-inline">\{i\}、精通技能</span>{i}、掌握专业${i}等相关知识和实践经验。`,
      proposedTeachingDirections: teachingDirsValues.length > 0 ? teachingDirsValues.sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 3) + 1) : [],
      // 修改这里：存储高校ID
      intendedUniversity1: availableUniversityIds.length > 0 ? availableUniversityIds[Math.floor(Math.random() * availableUniversityIds.length)] : null,
      intendedUniversity2: availableUniversityIds.length > 0 && Math.random() > 0.3 ? availableUniversityIds[Math.floor(Math.random() * availableUniversityIds.length)] : null,
      intendedUniversity3: availableUniversityIds.length > 0 && Math.random() > 0.5 ? availableUniversityIds[Math.floor(Math.random() * availableUniversityIds.length)] : null,
      personnelChanges: i % 5 === 0 ? `于2023年${i % 12 + 1}月有重要人事调整记录。` : '近期无显著人事变动',
      remarks: `干部${i}备注信息，例如授课风格偏好、特殊要求等。`,
    });
  }
  return initialData;
};

export const useCadreStore = defineStore('cadre', () => {
  const allCadres = ref([]);
  const isLoading = ref(false);
  const unitStore = useUnitStore(); // 获取 unitStore 实例
  const universityStore = useUniversityStore(); // 获取 universityStore 实例

  async function fetchCadres() {
    isLoading.value = true;
    // 确保依赖的 store 数据已加载 (在实际应用中很重要)
    if (unitStore.allUnits.length === 0) {
        // console.log('CadreStore: Fetching units for mock data generation...');
        await unitStore.fetchUnits(); // 假设 fetchUnits 会填充 allUnits
    }
    if (universityStore.rawUniversities.length === 0) {
        // console.log('CadreStore: Fetching universities for mock data generation...');
        await universityStore.fetchUniversities();
    }
    await new Promise(resolve => setTimeout(resolve, 100));
    allCadres.value = createInitialData(); // createInitialData 现在可以访问已加载的 unit/university 数据
    isLoading.value = false;
  }

  // ... 其他 addCadre, updateCadre, deleteCadre 方法 ...
  // 在 addCadre 和 updateCadre 中，确保提交的 cadreData 包含 unitId 而不是 unitAffiliation (如果字段名更改)
 async function addCadre(cadreData) {
    isLoading.value = true;
    await new Promise(resolve => setTimeout(resolve, 100)); // Simulate API
    const newCadre = {
      ...cadreData,
      id: `id-${Date.now()}-${Math.random().toString(36).substring(2, 9)}` // 确保 ID 唯一性
    };
    allCadres.value.unshift(newCadre); // 添加到列表开头
    isLoading.value = false;
    return newCadre; // 返回新创建的干部数据
  }

  async function updateCadre(cadreData) {
    isLoading.value = true;
    await new Promise(resolve => setTimeout(resolve, 100)); // Simulate API
    const index = allCadres.value.findIndex(item => item.id === cadreData.id);
    if (index !== -1) {
      allCadres.value[index] = { ...allCadres.value[index], ...cadreData };
      isLoading.value = false;
      return allCadres.value[index]; // 返回更新后的干部数据
    }
    isLoading.value = false;
    throw new Error('Cadre not found for update');
  }

  async function deleteCadre(cadreId) {
    isLoading.value = true;
    await new Promise(resolve => setTimeout(resolve, 100)); // Simulate API
    allCadres.value = allCadres.value.filter(item => item.id !== cadreId);
    isLoading.value = false;
  }

  return {
    allCadres,
    isLoading,
    fetchCadres,
    addCadre,
    updateCadre,
    deleteCadre,
  };
});