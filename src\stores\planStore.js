import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useCadreStore } from './cadreStore';
import { useUniversityStore } from './universityStore';
// 确保你的 planOptions.js 路径正确，并且导出了这些选项
import { teachingStatusOptions, semesterOptions, courseSeriesOptions } from '@/views/plan/planOptions';

// 辅助函数：生成更真实的模拟数据
const generateMockPlans = (count = 30) => {
  const plans = [];
  const cadreStore = useCadreStore();
  const universityStore = useUniversityStore();

  // 确保干部和高校数据已加载，用于生成模拟数据
  const availableCadres = cadreStore.allCadres.length > 0 
    ? cadreStore.allCadres 
    : [{ id: 'temp_cadre_0', name: '示例干部', proposedTeachingDirections: ['模拟主题1', '模拟主题2'] }];
  const availableUniversities = universityStore.rawUniversities.length > 0 
    ? universityStore.rawUniversities 
    : [{ id: 'temp_uni_0', schoolName: '示例大学' }];
  
  const statuses = teachingStatusOptions.map(s => s.value);
  const semesters = semesterOptions.map(s => s.value);
  const series = courseSeriesOptions.map(s => s.value);

  for (let i = 1; i <= count; i++) {
    const cadre = availableCadres[i % availableCadres.length];
    const university = availableUniversities[i % availableUniversities.length];
    
    const year = 2025 + Math.floor(i / 12); // Simulate different years
    const month = i % 12; // 0-11 for Date object
    const day = (i % 28) + 1;
    const teachingDate = new Date(year, month, day);

    const classStartTime = new Date(teachingDate);
    classStartTime.setHours((8 + i % 10), (i % 4) * 15, 0, 0); // 授课时间在 8:00 - 17:45 之间

    plans.push({
      id: `plan-${Date.now()}-${i}-${Math.random().toString(36).substring(2, 7)}`,
      cadreId: cadre.id,
      universityId: university.id,
      courseSeries: series[i % series.length],
      courseName: (cadre.proposedTeachingDirections && cadre.proposedTeachingDirections.length > 0)
                  ? cadre.proposedTeachingDirections[i % cadre.proposedTeachingDirections.length]
                  : `关于“${cadre.name}”的专题讲座 #${i}`,
      classDate: teachingDate.getTime(), // 存储为时间戳
      classTime: classStartTime.getTime(), // 存储为时间戳 (主要用于时间部分，但包含日期)
      location: `${university.schoolName} ${['主教学楼101室', '图书馆报告厅', '学院会议室A302'][i % 3]}`,
      plannedHours: ((i % 3) + 1) * 2, // 例如 2, 4, 6 学时
      teachingStatus: statuses[i % statuses.length] || 'planned',
      teachingSemester: semesters[i % semesters.length],
      remarks: `这是第${i}条授课计划的备注信息，请关注。`,
    });
  }
  return plans;
};

export const usePlanStore = defineStore('plan', () => {
  const allPlans = ref([]);
  const isLoading = ref(false);

  async function fetchPlans() {
    isLoading.value = true;
    const cadreStore = useCadreStore();
    const universityStore = useUniversityStore();

    // 确保依赖的store数据已加载，以便模拟数据生成时能用到
    if (cadreStore.allCadres.length === 0 && !cadreStore.isLoading) {
      console.log('PlanStore: Fetching cadres for mock data generation...');
      await cadreStore.fetchCadres();
    }
    if (universityStore.rawUniversities.length === 0 && !universityStore.loading) {
      console.log('PlanStore: Fetching universities for mock data generation...');
      await universityStore.fetchUniversities();
    }

    await new Promise(resolve => setTimeout(resolve, 250)); // 模拟API延迟
    // 仅当 allPlans 为空时生成新的模拟数据，避免重复
    if (allPlans.value.length === 0) {
        allPlans.value = generateMockPlans(40); // 生成40条模拟计划
    }
    isLoading.value = false;
  }

  async function addPlan(planData) {
    isLoading.value = true;
    await new Promise(resolve => setTimeout(resolve, 100));
    const newPlan = {
      ...planData,
      id: `plan-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    };
    allPlans.value.unshift(newPlan); // 新计划加到最前面
    isLoading.value = false;
    return newPlan;
  }

  async function updatePlan(planData) {
    isLoading.value = true;
    await new Promise(resolve => setTimeout(resolve, 100));
    const index = allPlans.value.findIndex(p => p.id === planData.id);
    if (index !== -1) {
      allPlans.value[index] = { ...allPlans.value[index], ...planData };
      isLoading.value = false;
      return allPlans.value[index];
    }
    isLoading.value = false;
    throw new Error('未找到要更新的授课计划');
  }

  async function deletePlan(planId) {
    isLoading.value = true;
    await new Promise(resolve => setTimeout(resolve, 100));
    allPlans.value = allPlans.value.filter(p => p.id !== planId);
    isLoading.value = false;
  }

  return {
    allPlans,
    isLoading,
    fetchPlans,
    addPlan,
    updatePlan,
    deletePlan,
  };
});