// src/stores/unitStore.js
import { defineStore } from 'pinia';
import { ref, reactive, computed } from 'vue';
import { unitTypeOptions, unitLevelOptions } from "/src/views/unit/unitOptions.js"; // 确保路径正确

export const useUnitStore = defineStore('unit', () => {
  // --- State ---
  const allUnits = ref([]);
  const loading = ref(false);
  const searchTerm = ref('');
  const filterUnitType = ref([]);
  const filterUnitLevel = ref([]);
  const sorter = ref(null);

  const pagination = reactive({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
  });

  const showModal = ref(false);
  const modalTitle = ref('新增单位');
  const editingUnit = ref(null);

  // --- Getters (Computed) ---
  const paginationConfig = computed(() => ({
    page: pagination.page,
    pageSize: pagination.pageSize,
    itemCount: pagination.itemCount,
    showSizePicker: pagination.showSizePicker,
    pageSizes: pagination.pageSizes,
  }));

  const paginatedData = computed(() => {
    // 注意：实际项目中，筛选和分页通常由后端完成。
    // 前端筛选和分页适用于数据量较小的情况。
    // 这里的实现是为了演示，如果你的 fetchUnits 已经是后端分页和筛选，
    // 那么这个 computed 属性可能只需要返回 allUnits.value 即可，
    // 因为 allUnits.value 就已经是后端处理过的当前页数据。
    // 但根据你当前的 generateMockData 和 fetchUnits 的模拟方式，
    // 前端处理是必要的。

    let dataToProcess = [...allUnits.value]; // 使用 allUnits 进行前端筛选和排序

    // 筛选逻辑 (如果 fetchUnits 已经是后端筛选，这部分可以简化或移除)
    if (searchTerm.value) {
      const term = searchTerm.value.toLowerCase();
      dataToProcess = dataToProcess.filter(
        item => (item.name && item.name.toLowerCase().includes(term)) ||
                (item.shortName && item.shortName.toLowerCase().includes(term)) ||
                (item.contactPerson && item.contactPerson.toLowerCase().includes(term))
      );
    }
    if (filterUnitType.value && filterUnitType.value.length > 0) {
      dataToProcess = dataToProcess.filter(item => filterUnitType.value.includes(item.unitType));
    }
    if (filterUnitLevel.value && filterUnitLevel.value.length > 0) {
      dataToProcess = dataToProcess.filter(item => filterUnitLevel.value.includes(item.unitLevel));
    }

    // 排序逻辑 (如果 fetchUnits 已经是后端排序，这部分可以简化或移除)
    if (sorter.value && sorter.value.order) {
      const { columnKey, order } = sorter.value;
      dataToProcess.sort((a, b) => {
        let valA = a[columnKey];
        let valB = b[columnKey];
        // 对于 undefined 或 null 的处理，可以根据需要调整
        if (valA == null) valA = '';
        if (valB == null) valB = '';

        if (typeof valA === 'string') valA = valA.toLowerCase();
        if (typeof valB === 'string') valB = valB.toLowerCase();
        
        if (valA < valB) return order === 'ascend' ? -1 : 1;
        if (valA > valB) return order === 'ascend' ? 1 : -1;
        return 0;
      });
    }
    
    // 前端分页 (如果 fetchUnits 已经是后端分页，这部分需要调整)
    // pagination.itemCount = dataToProcess.length; // 更新总数基于筛选后的数据
    // const startIndex = (pagination.page - 1) * pagination.pageSize;
    // return dataToProcess.slice(startIndex, startIndex + pagination.pageSize);
    
    // !!重要提示!!: 鉴于你的 fetchUnits 中的 generateMockData 已经实现了分页和筛选逻辑，
    // paginatedData 应该直接返回 allUnits.value，因为 allUnits 已经是后端（模拟）处理后的结果。
    // 如果 paginatedData 再次进行筛选和分页，会导致逻辑重复或错误。
    // 这里我将假设 allUnits 就是当前页应该显示的数据，由 fetchUnits 获取。
    // 如果 allUnits 是所有未分页的数据，那么上面的筛选和分页逻辑是需要的。
    // 根据你 fetchUnits 的实现，它获取的就是分页和筛选后的数据。
    return dataToProcess; // 假设 allUnits 已经是经过后端（模拟）筛选和分页的数据
  });

  // --- Actions ---
  async function fetchUnits() {
    loading.value = true;
    try {
      const params = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        searchTerm: searchTerm.value,
        unitType: filterUnitType.value, // 确保传递的是值
        unitLevel: filterUnitLevel.value, // 确保传递的是值
        sortKey: sorter.value?.columnKey,
        sortOrder: sorter.value?.order,
      };
      console.log('Fetching units with params:', params);
      // 模拟 API 调用延迟
      await new Promise(resolve => setTimeout(resolve, 300));

      const mockApiResponse = generateMockData(params);
      allUnits.value = mockApiResponse.items;
      pagination.itemCount = mockApiResponse.totalCount; // 总数由模拟API返回

    } catch (error) {
      console.error('Failed to fetch units:', error);
      allUnits.value = [];
      pagination.itemCount = 0;
      throw error;
    } finally {
      loading.value = false;
    }
  }

  function generateMockData(params) {
    let mockDataFull = Array.from({ length: 53 }).map((_, i) => ({
      id: i + 1,
      name: `演示单位 ${i + 1}`,
      shortName: `演示简称 ${i + 1}`,
      calibrationCode: `CAL-2025-${String(i + 1).padStart(4, '0')}`,
      receiptCode: `REC-2025-${String(i + 1).padStart(4, '0')}`, // 新增收件编号
      unitType: unitTypeOptions[i % unitTypeOptions.length].value,
      unitLevel: unitLevelOptions[i % unitLevelOptions.length].value,
      contactPerson: `联系人 ${i + 1}`,
      landlinePhone: `010-5555${String(i).padStart(4, '0')}`,
      mobilePhone: `13800138${String(i).padStart(3, '0')}`,
      remarks: `这是单位 ${i + 1} 的详细备注信息。`,
    }));

    // 模拟后端筛选
    if (params.searchTerm) {
      const term = params.searchTerm.toLowerCase();
      mockDataFull = mockDataFull.filter(
        item => (item.name && item.name.toLowerCase().includes(term)) ||
                (item.shortName && item.shortName.toLowerCase().includes(term)) ||
                (item.contactPerson && item.contactPerson.toLowerCase().includes(term))
      );
    }
    if (params.unitType && params.unitType.length > 0) {
        mockDataFull = mockDataFull.filter(item => params.unitType.includes(item.unitType));
    }
    if (params.unitLevel && params.unitLevel.length > 0) {
        mockDataFull = mockDataFull.filter(item => params.unitLevel.includes(item.unitLevel));
    }

    // 模拟后端排序
    if (params.sortKey && params.sortOrder) {
        mockDataFull.sort((a, b) => {
            let valA = a[params.sortKey];
            let valB = b[params.sortKey];
            if (valA == null) valA = ''; // 处理 null 或 undefined
            if (valB == null) valB = '';

            if (typeof valA === 'string') valA = valA.toLowerCase();
            if (typeof valB === 'string') valB = valB.toLowerCase();

            if (valA < valB) return params.sortOrder === 'ascend' ? -1 : 1;
            if (valA > valB) return params.sortOrder === 'ascend' ? 1 : -1;
            return 0;
        });
    }
    
    const totalCount = mockDataFull.length;

    // 模拟后端分页
    const startIndex = (params.page - 1) * params.pageSize;
    const endIndex = startIndex + params.pageSize;
    const itemsForPage = mockDataFull.slice(startIndex, endIndex);

    return { items: itemsForPage, totalCount };
  }

  async function submitUnit(formData) {
    loading.value = true;
    // const message = useMessage(); // Pinia store中不建议直接使用 UI库的message，应由组件调用
    try {
      if (editingUnit.value && editingUnit.value.id) { // 编辑
        console.log('Updating unit (in store):', formData);
        await new Promise(resolve => setTimeout(resolve, 500));
        // 实际应用中，这里会发送 PUT 请求到后端
        // const response = await api.updateUnit(formData.id, formData);
        // allUnits.value = allUnits.value.map(u => u.id === formData.id ? response.data : u);
        const index = allUnits.value.findIndex(u => u.id === editingUnit.value.id);
        if (index !== -1) {
          // 更新本地的 allUnits 数组，更真实的场景是重新 fetchUnits 或者后端返回更新后的对象
          allUnits.value.splice(index, 1, { ...editingUnit.value, ...formData });
        }
      } else { // 新增
        console.log('Creating new unit (in store):', formData);
        await new Promise(resolve => setTimeout(resolve, 500));
        // 实际应用中，这里会发送 POST 请求到后端
        // const response = await api.createUnit(formData);
        // allUnits.value.unshift(response.data); // 或者 push，然后重新 fetch
        const newId = allUnits.value.length > 0 ? Math.max(...allUnits.value.map(u => u.id)) + 1 : 1;
        // allUnits.value.unshift({ ...formData, id: newId }); // 添加到开头
        // 为了让分页和总数正确，通常新增后会重新请求数据
      }
      closeTheModal();
      await fetchUnits(); // 重新获取数据以保证列表最新（包括分页和总数）
      return { success: true, message: `单位 "${formData.name}" 操作成功` };
    } catch (error) {
      console.error('Form submission failed (in store):', error);
      return { success: false, message: '操作失败，请重试', error };
    } finally {
      loading.value = false;
    }
  }

  async function deleteUnit(unitId, unitName) { // Renamed from deleteUnitById to deleteUnit
    loading.value = true;
    try {
      console.log('Deleting unit (in store):', unitId);
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call
      // 实际应用中，这里会发送 DELETE 请求到后端
      // await api.deleteUnit(unitId);
      // allUnits.value = allUnits.value.filter(u => u.id !== unitId);
      // 通常删除后会重新请求数据
      await fetchUnits(); // 重新获取数据
      return { success: true, message: `单位 "${unitName}" 删除成功` };
    } catch (error) {
      console.error('Delete operation failed (in store):', error);
      return { success: false, message: '删除操作失败，请重试', error };
    } finally {
      loading.value = false;
    }
  }


  function openTheCreateModal() {
    modalTitle.value = '新增单位';
    editingUnit.value = null; // 清空编辑数据
    showModal.value = true;
  }

  function openTheEditModal(unit) {
    modalTitle.value = '编辑单位';
    // 创建一个副本进行编辑，避免直接修改列表中的对象
    editingUnit.value = { ...unit };
    showModal.value = true;
  }

  function closeTheModal() {
    showModal.value = false;
    // editingUnit.value = null; // 可以在 modal @after-leave 时重置
  }
  
  function handleModalClosed() {
    editingUnit.value = null; // 在模态框完全关闭后重置，避免表单内容闪烁
  }


  function updateSearchTerm(newTerm) {
    searchTerm.value = newTerm;
    pagination.page = 1; // 重置到第一页
    fetchUnits().catch(err => console.error("Error during fetch in updateSearchTerm:", err));
  }

  // 防抖的搜索更新
  let searchTimeout = null;
  function debouncedUpdateSearchTerm(newTerm) {
    // searchTerm.value = newTerm; // 这个可以立即更新输入框的显示，但实际的搜索会延迟
    // 如果希望输入框的值也延迟同步到 searchTerm，则将下一行移到 setTimeout 外
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      searchTerm.value = newTerm; // 在执行搜索前更新searchTerm
      pagination.page = 1;
      fetchUnits().catch(err => console.error("Error during debounced fetch:", err));
    }, 500); // 500ms 防抖延迟
  }


  function updateFilterUnitType(types) {
    filterUnitType.value = types;
    pagination.page = 1;
    fetchUnits().catch(err => console.error("Error during fetch in updateFilterUnitType:", err));
  }

  function updateFilterUnitLevel(levels) {
    filterUnitLevel.value = levels;
    pagination.page = 1;
    fetchUnits().catch(err => console.error("Error during fetch in updateFilterUnitLevel:", err));
  }

  function handlePageChange(page) {
    pagination.page = page;
    fetchUnits().catch(err => console.error("Error during fetch in handlePageChange:", err));
  }

  function handlePageSizeChange(newPageSize) {
    pagination.pageSize = newPageSize;
    pagination.page = 1; //切换每页数量时，通常回到第一页
    fetchUnits().catch(err => console.error("Error during fetch in handlePageSizeChange:", err));
  }

  function handleSorterChange(newSorter) {
    // newSorter 可能是单个 sorter 对象，也可能是 null (取消排序)
    // 或者是一个包含 columnKey 和 order 的对象
    if (newSorter && newSorter.order) {
      sorter.value = { columnKey: newSorter.columnKey, order: newSorter.order };
    } else {
      sorter.value = null; // 清除排序状态
    }
    pagination.page = 1; // 排序改变，回到第一页
    fetchUnits().catch(err => console.error("Error during fetch in handleSorterChange:", err));
  }

  return {
    allUnits, loading, searchTerm, filterUnitType, filterUnitLevel, sorter, pagination,
    showModal, modalTitle, editingUnit,
    paginationConfig, // 用于 n-data-table 的 :pagination
    paginatedData,    // 用于 n-data-table 的 :data

    unitTypeFilterOptions: computed(() => unitTypeOptions), // 作为 computed 属性提供
    unitLevelFilterOptions: computed(() => unitLevelOptions), // 作为 computed 属性提供

    fetchUnits,
    submitUnit,
    deleteUnit, // 修改了名称
    openTheCreateModal,
    openTheEditModal,
    closeTheModal,
    handleModalClosed,

    updateSearchTerm, // 直接更新
    debouncedUpdateSearchTerm, // 防抖更新

    updateFilterUnitType,
    updateFilterUnitLevel,
    handlePageChange,
    handlePageSizeChange,
    handleSorterChange,
  };
});