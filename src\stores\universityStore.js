import { defineStore } from 'pinia';
import { ref } from 'vue';
// 确保这些选项文件路径正确，并且它们提供的数据仍然适用
import { governingDepartmentOptions, locationOptions } from '@/views/universities/universityOptions'; 

// 真实的中国大学名称列表 (这里提供部分示例，你可以根据需要扩展)
const realChineseUniversityNames = [
  "北京大学", "清华大学", "复旦大学", "上海交通大学", "浙江大学",
  "南京大学", "中国科学技术大学", "哈尔滨工业大学", "西安交通大学", "武汉大学",
  "四川大学", "中山大学", "华中科技大学", "同济大学", "天津大学",
  "南开大学", "北京师范大学", "厦门大学", "东南大学", "中国人民大学",
  "北京航空航天大学", "山东大学", "吉林大学", "重庆大学", "电子科技大学",
  "大连理工大学", "中南大学", "西北工业大学", "华南理工大学", "兰州大学",
  "中国农业大学", "国防科技大学", "湖南大学", "东北大学", "郑州大学",
  "云南大学", "新疆大学", "北京理工大学", "华东师范大学", "中国海洋大学",
  // 你可以继续添加更多大学名称，确保列表长度至少为你希望生成的模拟数据数量
  // 为了满足你原有的 length: 38，我再补充一些
  "西北农林科技大学", "中央民族大学", "中国政法大学", "对外经济贸易大学", "中央财经大学",
  "上海财经大学", "中南财经政法大学", "西南财经大学", "北京邮电大学", "南京邮电大学",
  "西安电子科技大学", "北京交通大学", "西南交通大学", "上海大学", "苏州大学" 
  // 目前列表长度超过38，下面的代码会循环使用或随机选取
];


// 修改后的模拟数据生成函数
const generateMockUniversities = () => {
  const numberOfUniversitiesToGenerate = 38; // 你原来代码中的生成数量
  
  return Array.from({ length: numberOfUniversitiesToGenerate }).map((_, i) => {
    // 循环使用提供的真实大学名称列表
    const schoolName = realChineseUniversityNames[i % realChineseUniversityNames.length];
    
    // 如果希望每次生成的大学名称尽量不重复（在列表足够长的情况下）
    // 且生成数量不超过列表长度，可以直接用 realChineseUniversityNames[i]
    // const schoolName = (i < realChineseUniversityNames.length) ? realChineseUniversityNames[i] : `补充大学 ${i + 1}`;


    return {
      id: i + 1, // ID 仍然从1开始自增
      schoolName: schoolName, // 使用真实的大学名称
      schoolCode: `UCODE${String(10001 + i).padStart(5, '0')}`, // 学校代码生成逻辑不变
      // 主管部门和地点从 options 文件中随机选取，保持不变
      governingDepartment: governingDepartmentOptions[i % governingDepartmentOptions.length].value,
      location: locationOptions[i % locationOptions.length].value,
      isCentral: i % 3 === 0, // 是否部委直属的模拟逻辑不变
      // 以下联系方式等信息继续使用模拟生成方式
      contactDepartment: `${schoolName}招生办`, // 可以稍微关联一下名称
      contactPosition: ['主任', '副主任', '招生主管', '老师'][i % 4],
      contactName: `招办老师${i + 1}`, // 例如：王老师、李老师等可以更真实，但目前保持简单
      contactMethod: `13${String(Math.floor(Math.random()*10)+8)}00${String(i+10).padStart(3, '0')}${String(i).padStart(2,'0')}`.slice(0,11), // 模拟手机号
      remarks: `这是 ${schoolName} 的备注信息，提供各类学科专业。`,
    };
  });
}

// 用于存储实际数据的非响应式变量，避免在每次 action 中重新生成
let storeMockRawUniversities = [];

export const useUniversityStore = defineStore('universities', () => {
  // State
  const rawUniversities = ref([]);
  const loading = ref(false);

  // Actions
  async function fetchUniversities() {
    loading.value = true;
    // 模拟 API 请求延迟
    await new Promise(resolve => setTimeout(resolve, 200));
    if (storeMockRawUniversities.length === 0) { // 仅在第一次获取时生成
        storeMockRawUniversities = generateMockUniversities();
    }
    rawUniversities.value = [...storeMockRawUniversities]; // 使用展开运算符创建新数组以触发响应性
    loading.value = false;
  }

  async function addUniversity(universityData) {
    loading.value = true;
    await new Promise(resolve => setTimeout(resolve, 100)); // 模拟 API 延迟
    const newId = storeMockRawUniversities.length > 0 ? Math.max(...storeMockRawUniversities.map(u => u.id)) + 1 : 1;
    const newUniversity = { ...universityData, id: newId };
    storeMockRawUniversities.push(newUniversity);
    rawUniversities.value = [...storeMockRawUniversities];
    loading.value = false;
    return newUniversity; // 返回新创建的对象，方便组件处理
  }

  async function updateUniversity(universityData) {
    loading.value = true;
    await new Promise(resolve => setTimeout(resolve, 100)); // 模拟 API 延迟
    const index = storeMockRawUniversities.findIndex(u => u.id === universityData.id);
    if (index !== -1) {
      storeMockRawUniversities[index] = { ...storeMockRawUniversities[index], ...universityData }; // 确保是合并，而不是完全替换
      rawUniversities.value = [...storeMockRawUniversities];
    }
    loading.value = false;
    return universityData; // 返回更新后的对象
  }

  async function deleteUniversity(universityId) {
    loading.value = true;
    await new Promise(resolve => setTimeout(resolve, 100)); // 模拟 API 延迟
    const index = storeMockRawUniversities.findIndex(u => u.id === universityId);
    if (index !== -1) {
      storeMockRawUniversities.splice(index, 1);
      rawUniversities.value = [...storeMockRawUniversities];
    }
    loading.value = false;
  }

  return {
    rawUniversities,
    loading,
    fetchUniversities,
    addUniversity,
    updateUniversity,
    deleteUniversity,
  };
});