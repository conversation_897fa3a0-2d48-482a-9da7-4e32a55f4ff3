<template>
  <n-form ref="formRef" :model="formValue" :rules="rules" label-placement="left" label-width="auto" require-mark-placement="right-hanging">
    <n-grid :cols="24" :x-gap="24">
      <n-form-item-gi :span="12" label="姓名" path="name">
        <n-input v-model:value="formValue.name" placeholder="请输入姓名" />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="性别" path="gender">
        <n-select
          v-model:value="formValue.gender"
          placeholder="请选择性别"
          :options="genderOptions"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="出生年月" path="birthDate">
        <n-date-picker v-model:value="formValue.birthDate" type="date" placeholder="请选择出生年月" class="w-full" />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="籍贯" path="nativePlace">
        <n-input v-model:value="formValue.nativePlace" placeholder="请输入籍贯" />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="级别" path="level">
        <n-select
          v-model:value="formValue.level"
          placeholder="请选择级别"
          :options="levelOptions"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="职务" path="position">
        <n-input v-model:value="formValue.position" placeholder="请输入职务" />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="干部类型" path="cadreType">
        <n-select
          v-model:value="formValue.cadreType"
          placeholder="请选择干部类型"
          :options="cadreTypeOptions"
        />
      </n-form-item-gi>
      
      <n-form-item-gi :span="12" label="单位归属" path="unitId">
        <n-select
          v-model:value="formValue.unitId"
          filterable
          placeholder="请选择单位归属"
          :options="unitSelectOptions"
          :loading="unitStore.loading"
          clearable
        />
      </n-form-item-gi>

      <n-form-item-gi :span="12" label="最高学历" path="highestEducation">
        <n-select
          v-model:value="formValue.highestEducation"
          placeholder="请选择最高学历"
          :options="educationOptions"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="12" label="最高学位" path="highestDegree">
        <n-select
          v-model:value="formValue.highestDegree"
          placeholder="请选择最高学位"
          :options="degreeOptions"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="24" label="熟悉专长" path="expertise">
        <n-input
          v-model:value="formValue.expertise"
          type="textarea"
          placeholder="请输入熟悉专长"
          :autosize="{ minRows: 2 }"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="24" label="拟授课方向" path="proposedTeachingDirections">
        <n-select
          v-model:value="formValue.proposedTeachingDirections"
          multiple
          filterable
          placeholder="请选择拟授课方向 (最多选2个)"
          :options="teachingDirectionOptions"
          :max-tag-count="2" 
        />
      </n-form-item-gi>

      <n-form-item-gi :span="8" label="意向授课高校1" path="intendedUniversity1">
        <n-select
          v-model:value="formValue.intendedUniversity1"
          filterable
          placeholder="请选择意向授课高校1"
          :options="universitySelectOptions"
          :loading="universityStore.loading"
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi :span="8" label="意向授课高校2" path="intendedUniversity2">
        <n-select
          v-model:value="formValue.intendedUniversity2"
          filterable
          placeholder="请选择意向授课高校2 (可选)"
          :options="universitySelectOptions"
          :loading="universityStore.loading"
          clearable
        />
      </n-form-item-gi>
      <n-form-item-gi :span="8" label="意向授课高校3" path="intendedUniversity3">
        <n-select
          v-model:value="formValue.intendedUniversity3"
          filterable
          placeholder="请选择意向授课高校3 (可选)"
          :options="universitySelectOptions"
          :loading="universityStore.loading"
          clearable
        />
      </n-form-item-gi>

      <n-form-item-gi :span="24" label="人事变动" path="personnelChanges">
        <n-input
          v-model:value="formValue.personnelChanges"
          type="textarea"
          placeholder="请输入人事变动情况"
          :autosize="{ minRows: 2 }"
        />
      </n-form-item-gi>
      <n-form-item-gi :span="24" label="备注" path="remarks">
        <n-input
          v-model:value="formValue.remarks"
          type="textarea"
          placeholder="请输入备注信息"
          :autosize="{ minRows: 2 }"
        />
      </n-form-item-gi>
    </n-grid>
    <div class="flex justify-end mt-6">
      <n-button @click="handleCancel" class="mr-4">取消</n-button>
      <n-button type="primary" @click="handleValidateAndSubmit">
        {{ initialData ? '保存更新' : '创建' }}
      </n-button>
    </div>
  </n-form>
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue';
import {
  NForm,
  NFormItemGi,
  NInput,
  NSelect,
  NDatePicker,
  NButton,
  NGrid,
  useMessage,
} from 'naive-ui';
import {
  genderOptions,
  levelOptions,
  cadreTypeOptions,
  educationOptions,
  degreeOptions,
  teachingDirectionOptions,
  // universityOptions as localUniversityOptions, // 不再使用本地的
} from './cadreOptions'; // 假设在同一目录下, 或修正为正确路径 @/views/cadres/cadreOptions

import { useUnitStore } from '@/stores/unitStore';
import { useUniversityStore } from '@/stores/universityStore';

const message = useMessage();
const formRef = ref(null);

const props = defineProps({
  initialData: { // 用于编辑时回填数据
    type: Object,
    default: () => null
  }
});

const emit = defineEmits(['submit', 'cancel']);

const unitStore = useUnitStore();
const universityStore = useUniversityStore();

const getDefaultFormValue = () => ({
  name: '',
  gender: null,
  birthDate: null, // Naive UI DatePicker v-model:value 默认是时间戳 (number)
  nativePlace: '',
  level: null,
  position: '',
  cadreType: null,
  unitId: null, // 对应单位归属的选择器，存储单位ID
  highestEducation: null,
  highestDegree: null,
  expertise: '',
  proposedTeachingDirections: [],
  intendedUniversity1: null, // 存储高校ID
  intendedUniversity2: null, // 存储高校ID
  intendedUniversity3: null, // 存储高校ID
  personnelChanges: '',
  remarks: '',
});

const formValue = ref(getDefaultFormValue());

// 监听 initialData 变化，用于编辑时填充表单
watch(() => props.initialData, (newData) => {
  if (newData) {
    // 确保 newData 中的 birthDate (如果是字符串) 转换为时间戳
    const dataToSet = { ...getDefaultFormValue(), ...newData };
    if (dataToSet.birthDate && typeof dataToSet.birthDate === 'string') {
        const parsedDate = Date.parse(dataToSet.birthDate);
        if (!isNaN(parsedDate)) {
            dataToSet.birthDate = parsedDate;
        } else {
            dataToSet.birthDate = null; // 或者保留原样或给出错误提示
        }
    } else if (dataToSet.birthDate && typeof dataToSet.birthDate !== 'number') {
        // 如果已经是 Date 对象或其他非数字类型，尝试转换为时间戳
        const dateObj = new Date(dataToSet.birthDate);
        if (!isNaN(dateObj.getTime())) {
            dataToSet.birthDate = dateObj.getTime();
        } else {
            dataToSet.birthDate = null;
        }
    }
    formValue.value = dataToSet;
  } else {
    formValue.value = getDefaultFormValue();
  }
}, { immediate: true, deep: true });


const rules = {
  name: { required: true, message: '请输入姓名', trigger: ['input', 'blur'] },
  gender: { required: true, message: '请选择性别', trigger: ['change', 'blur'], type: 'string' }, // 假设 genderOptions 的 value 是 string
  birthDate: { required: true, message: '请选择出生年月', trigger: ['change', 'blur'], type: 'number' }, // Naive UI date-picker v-model:value 是时间戳 (number)
  level: { required: false, message: '请选择级别', trigger: 'change', type: 'string'}, // 根据你的实际需要调整 required 和 type
  unitId: { required: true, message: '请选择单位归属', trigger: ['change', 'blur'], type: 'number' }, // 假设单位ID是数字
  proposedTeachingDirections: {
    type: 'array',
    validator: (rule, value) => {
      if (!value || value.length === 0) {
        return new Error('请至少选择一个拟授课方向');
      }
      if (value.length > 2) { // 你的mock数据是最多3个，这里是2个，按需调整
        return new Error('拟授课方向最多选择两个');
      }
      return true;
    },
    trigger: ['change', 'blur'],
  },
  intendedUniversity1: { required: true, message: '请选择意向授课高校1', trigger: 'change', type: 'number' }, // 假设高校ID是数字
  // intendedUniversity2, intendedUniversity3 可以设置为非必填
  // nativePlace, position 等其他字段的验证规则可按需添加
};

// 从 Store 获取单位选项
const unitSelectOptions = computed(() => {
  return unitStore.allUnits.map(unit => ({
    label: unit.name, // 或者 unit.shortName
    value: unit.id // 确保 unit.id 是 number 类型以匹配 rule type
  }));
});

// 从 Store 获取高校选项
const universitySelectOptions = computed(() => {
  return universityStore.rawUniversities.map(uni => ({
    label: uni.schoolName,
    value: uni.id // 确保 uni.id 是 number 类型以匹配 rule type
  }));
});

onMounted(() => {
  // 进入表单时，确保相关的 store 数据已加载
  // 单位和高校数据量不大的话，可以一次性获取
  if (unitStore.allUnits.length === 0 && !unitStore.loading) {
    unitStore.fetchUnits(); // 假设 fetchUnits 获取所有单位用于选择器
  }
  if (universityStore.rawUniversities.length === 0 && !universityStore.loading) {
    universityStore.fetchUniversities();
  }
});


const handleValidateAndSubmit = (e) => {
  e.preventDefault();
  formRef.value?.validate((errors) => {
    if (!errors) {
      const dataToSubmit = { ...formValue.value };
      // 确保 birthDate 是时间戳 (number)
      if (dataToSubmit.birthDate && !(typeof dataToSubmit.birthDate === 'number')) {
        dataToSubmit.birthDate = new Date(dataToSubmit.birthDate).getTime();
      }
      emit('submit', dataToSubmit);
    } else {
      console.log('Validation errors:', errors);
      message.error('请检查表单填写是否正确');
    }
  });
};

const resetForm = () => {
  formValue.value = getDefaultFormValue();
  if (formRef.value) {
    formRef.value.restoreValidation();
  }
  // console.log('CadreForm.vue: 表单已通过内部 resetForm 方法重置。');
};

const handleCancel = () => {
  emit('cancel');
  // resetForm(); // 取消时是否重置表单，根据你的父组件逻辑决定
};

defineExpose({
  resetForm,
});
</script>