<template>
  <div class="px-4 sm:px-8 md:px-16 lg:px-48 pt-4 w-full">
    <div class="flex flex-col">
      <n-card :title="'干部管理'">
        <template #header-extra>
          <n-button :render-icon="renderAddIcon" type="primary" @click="openCreateModal" class="mr-2">
            新增干部信息
          </n-button>
          <n-button :render-icon="renderExportIcon" type="success" disabled>
            导出至Excel
          </n-button>
        </template>

        <div class="flex flex-wrap gap-4 mt-4 items-center">
          <div>
            <n-input v-model:value="searchTerm" placeholder="按姓名、单位搜索..." clearable @input="handleSearchDebounced"
              @clear="handleSearchDebounced">
              <template #prefix>
                <n-icon :component="SearchOutline" />
              </template>
            </n-input>
          </div>
          <div class="min-w-48">
            <n-select v-model:value="filterLevel" placeholder="选择级别" :options="levelFilterOptions" multiple
              filterable clearable @update:value="applyTableFilters" />
          </div>
          <div class="min-w-48">
            <n-select v-model:value="filterCadreType" placeholder="选择干部类型" :options="cadreTypeFilterOptions" multiple
              filterable clearable @update:value="applyTableFilters" />
          </div>
          <div class="flex-1" />
        </div>

        <div class="mt-6">
          <n-data-table
            ref="dataTableInstRef"
            :columns="columns"
            :data="paginatedData"
            :loading="cadreStore.isLoading || unitStore.loading || universityStore.loading"
            :pagination="paginationConfig"
            remote
            @update:page="handlePageChange"
            @update:page-size="handlePageSizeChange"
            @update:sorter="handleSorterChange"
            class="min-w-full"
            :scroll-x="2500"
          />
        </div>
      </n-card>
    </div>
  </div>

  <n-modal v-model:show="showModal" preset="card" :style="{ width: '800px' }" :title="modalTitle" size="huge"
    :bordered="false" :segmented="{ content: 'soft', footer: 'soft' }" closable @after-leave="handleModalClose">
    <cadre-form ref="cadreFormRef" :initial-data="editingCadre" @submit="handleFormSubmit" @cancel="closeModal" />
  </n-modal>

  <n-modal
    v-model:show="showPlanModal"
    preset="card"
    :style="{ width: '900px' }"
    :title="planModalTitle"
    size="huge"
    :bordered="false"
    :segmented="{ content: 'soft', footer: 'soft' }"
    closable
    @after-leave="handlePlanModalClose"
  >
    <plan-form
      ref="planFormRef"
      :initial-data="planInitialData" 
      @submit="handlePlanFormSubmit"
      @cancel="closePlanModal"
    />
  </n-modal>
</template>

<script setup>
import { AddOutline as Add, SearchOutline, DownloadOutline as ExportIcon } from "@vicons/ionicons5";
import { h, ref, onMounted, watch, computed } from "vue";
import {
  NIcon,
  NDataTable,
  NButton,
  useMessage,
  NInput,
  NSelect,
  NModal,
  NEllipsis,
  NCard,
  useDialog,
} from "naive-ui";
import CadreForm from './CadreForm.vue'; // 确保路径正确
import PlanForm from '../plan/PlanForm.vue'; // 确保路径正确
import { teachingDirectionOptions } from './cadreOptions'; // 确保路径正确

import { useCadreStore } from '@/stores/cadreStore';
import { useUnitStore } from '@/stores/unitStore';
import { useUniversityStore } from '@/stores/universityStore';

const message = useMessage();
const dialog = useDialog();
const cadreStore = useCadreStore();
const unitStore = useUnitStore();
const universityStore = useUniversityStore();

const dataTableInstRef = ref(null);

// --- Modal control for CadreForm ---
const showModal = ref(false);
const modalTitle = ref('新增干部信息');
const editingCadre = ref(null);
const cadreFormRef = ref(null);

// --- Modal control for PlanForm ---
const showPlanModal = ref(false);
const planModalTitle = ref('为干部排课');
const currentSchedulingCadre = ref(null); // Stores the cadre for whom the plan is being made
const planInitialData = ref(null); // Data to pass to PlanForm, like cadreId
const planFormRef = ref(null);


const openCreateModal = () => {
  editingCadre.value = null; // 清除上一次的编辑数据
  modalTitle.value = '新增干部信息';
  // 确保 CadreForm 被渲染后调用 resetForm
  showModal.value = true;
  // CadreForm 的 resetForm 最好在 modal 的 after-enter 或 CadreForm 自身的 onMounted 中调用，
  // 或者通过 watch showModal 来触发，确保 cadreFormRef.value 存在。
  // 目前 handleModalClose 会在关闭后重置，新增时也应确保是干净的表单。
  // 如果 CadreForm 内部有 watch initialData 来重置，那么设为 null 应该能触发。
};

const openEditModal = (rowData) => {
  // 传递给 CadreForm 的 initialData 应该是 Cadre 对象的完整副本
  // 确保 rowData 包含 unitId, intendedUniversity1/2/3 等ID字段
  editingCadre.value = { ...rowData };
  modalTitle.value = '编辑干部信息';
  showModal.value = true;
};

const closeModal = () => {
  showModal.value = false;
};

const handleModalClose = () => {
  // 在模态框完全关闭后重置表单，避免内容闪烁
  if (cadreFormRef.value && typeof cadreFormRef.value.resetForm === 'function') {
    cadreFormRef.value.resetForm();
  }
  editingCadre.value = null;
};

const handleFormSubmit = async (formData) => {
  try {
    if (editingCadre.value && editingCadre.value.id) {
      // 确保提交的 formData 包含正确的 id 和其他字段如 unitId
      await cadreStore.updateCadre({ ...formData, id: editingCadre.value.id });
      message.success('干部信息更新成功');
    } else {
      await cadreStore.addCadre(formData);
      message.success('干部信息新增成功');
    }
    closeModal();
    // 提交后，cadreStore.allCadres 会更新，watch 会触发 filterAndPaginateData
  } catch (error) {
    console.error("表单提交失败:", error);
    message.error(editingCadre.value ? '更新失败，请重试' : '新增失败，请重试');
  }
};

// --- Functions for Plan Modal ---
const openScheduleModal = (cadre) => {
  currentSchedulingCadre.value = { id: cadre.id, name: cadre.name }; // 保存当前操作的干部信息
  planModalTitle.value = `为 ${cadre.name} 排课`;
  // 初始化 PlanForm 所需的数据，至少包含 cadreId
  planInitialData.value = {
    cadreId: cadre.id, // PlanForm 将使用此ID来获取干部的意向高校
    cadreName: cadre.name, // 可选，用于显示
    // 其他可能需要的初始值
  };
  // 确保 PlanForm 被渲染后调用 resetForm
  showPlanModal.value = true;
};

const closePlanModal = () => {
  showPlanModal.value = false;
};

const handlePlanModalClose = () => {
  if (planFormRef.value && typeof planFormRef.value.resetForm === 'function') {
    planFormRef.value.resetForm();
  }
  currentSchedulingCadre.value = null;
  planInitialData.value = null;
};

const handlePlanFormSubmit = async (formDataFromPlanForm) => {
  // 此处应调用 planStore 的 action 来保存授课记录
  console.log('收到的排课表单数据:', formDataFromPlanForm);
  // formDataFromPlanForm 应该包含 cadreId, universityId, 授课日期等
  const cadreNameForMessage = currentSchedulingCadre.value?.name || '该干部';
  try {
    // 模拟 API 调用
    // await planStore.addTeachingRecord(formDataFromPlanForm);
    await new Promise(resolve => setTimeout(resolve, 500));
    message.success(`为干部 ${cadreNameForMessage} 新增授课计划成功 (模拟)`);
    closePlanModal();
    // 如果授课记录的增删改查会影响干部列表的某些状态，可能需要重新获取干部数据
    // await cadreStore.fetchCadres(); 
  } catch(error) {
    message.error('排课失败，请重试');
    console.error("排课提交失败:", error);
  }
};

// --- Table Column Definitions (Maps and Helper) ---
const genderMap = { male: '男', female: '女', other: '其他' };
const levelMap = { provincial: '省级', departmental: '厅局级', county: '县处级', township: '乡科级', other_level: '其他' };
const cadreTypeMap = { party_government: '党政干部', professional_technical: '专业技术干部', enterprise_management: '企业管理干部', other_cadre_type: '其他' };
const educationMap = { doctorate: '博士研究生', master: '硕士研究生', bachelor: '本科', associate: '大专', high_school: '高中', other_education: '其他' };
const degreeMap = { phd: '博士', master_degree: '硕士', bachelor_degree: '学士', none: '无' };

// Options for filters
const levelFilterOptions = ref(Object.entries(levelMap).map(([value, label]) => ({ label, value })));
const cadreTypeFilterOptions = ref(Object.entries(cadreTypeMap).map(([value, label]) => ({ label, value })));

// --- Icon Rendering ---
const renderAddIcon = () => h(NIcon, null, { default: () => h(Add) });
const renderExportIcon = () => h(NIcon, null, { default: () => h(ExportIcon) });

// --- Data Mapping for Display ---
const teachingDirectionDisplayMap = Object.fromEntries(
  teachingDirectionOptions.map(opt => [opt.value, opt.label])
);

const unitNameMap = computed(() => {
  const map = {};
  unitStore.allUnits.forEach(unit => {
    map[unit.id] = unit.name; // 假设单位对象有 id 和 name 属性
  });
  return map;
});

const universityNameMap = computed(() => {
  const map = {};
  universityStore.rawUniversities.forEach(uni => {
    map[uni.id] = uni.schoolName; // 假设高校对象有 id 和 schoolName 属性
  });
  return map;
});

const getDisplayValue = (value, map, defaultValue = '未知') => map[value] || value || defaultValue;

// 模拟的状态选项 (如果干部数据中有 status 字段)
const statusOptions = [
  { label: '在职', value: 'active' },
  { label: '借调', value: 'seconded' },
  // ...其他状态
];
const statusDisplayMap = Object.fromEntries(statusOptions.map(opt => [opt.value, opt.label]));


const createColumns = ({ onEdit, onSchedule, onDelete }) => {
  return [
    {
      title: '序号',
      key: 'tableId',
      width: 60,
      fixed: 'left',
      render: (_, index) => (paginationConfig.value.page - 1) * paginationConfig.value.pageSize + index + 1
    },
    { title: '姓名', key: 'name', width: 100, sorter: true, fixed: 'left', ellipsis: { tooltip: true } },
    {
      title: '性别', key: 'gender', width: 70,
      render: (row) => getDisplayValue(row.gender, genderMap)
    },
    {
      title: '出生年月', key: 'birthDate', width: 120, sorter: true,
      render: (row) => row.birthDate ? new Date(row.birthDate).toLocaleDateString() : 'N/A'
    },
    { title: '籍贯', key: 'nativePlace', width: 100, ellipsis: { tooltip: true } },
    {
      title: '级别', key: 'level', width: 100, sorter: true,
      render: (row) => getDisplayValue(row.level, levelMap)
    },
    { title: '职务', key: 'position', width: 120, ellipsis: { tooltip: true } },
    {
      title: '干部类型', key: 'cadreType', width: 120,
      render: (row) => getDisplayValue(row.cadreType, cadreTypeMap)
    },
    { 
      title: '单位归属', 
      key: 'unitId', // 使用 unitId
      width: 180, 
      sorter: true, // 按 ID 排序，或自定义按名称排序
      render: (row) => h(NEllipsis, { tooltip: { style: { maxWidth: '300px' }}}, { default: () => unitNameMap.value[row.unitId] || '未分配' })
    },
    {
      title: '最高学历', key: 'highestEducation', width: 120,
      render: (row) => getDisplayValue(row.highestEducation, educationMap)
    },
    {
      title: '最高学位', key: 'highestDegree', width: 100,
      render: (row) => getDisplayValue(row.highestDegree, degreeMap)
    },
    {
      title: '熟悉专长', key: 'expertise', width: 180,
      render: row => h(NEllipsis, { tooltip: { style: { maxWidth: '300px' } } }, { default: () => row.expertise || 'N/A' })
    },
    {
      title: '拟授课方向',
      key: 'proposedTeachingDirections',
      width: 200,
      render: (row) => {
        if (Array.isArray(row.proposedTeachingDirections) && row.proposedTeachingDirections.length > 0) {
          const directions = row.proposedTeachingDirections.map(dir => teachingDirectionDisplayMap[dir] || dir).join('、');
          return h(NEllipsis, { tooltip: { style: { maxWidth: '300px' } } }, { default: () => directions });
        }
        return 'N/A';
      }
    },
    {
      title: '意向授课高校1', key: 'intendedUniversity1', width: 180,
      render: row => h(NEllipsis, { tooltip: {style: {maxWidth: '300px'}}}, { default: () => universityNameMap.value[row.intendedUniversity1] || 'N/A' })
    },
    {
      title: '意向授课高校2', key: 'intendedUniversity2', width: 180,
      render: row => h(NEllipsis, { tooltip: {style: {maxWidth: '300px'}}}, { default: () => universityNameMap.value[row.intendedUniversity2] || 'N/A' })
    },
    {
      title: '意向授课高校3', key: 'intendedUniversity3', width: 180,
      render: row => h(NEllipsis, { tooltip: {style: {maxWidth: '300px'}}}, { default: () => universityNameMap.value[row.intendedUniversity3] || 'N/A' })
    },
    {
      title: '状态', key: 'status', width: 100, // 假设干部数据中有 status 字段
      render: (row) => getDisplayValue(row.status, statusDisplayMap, '未知')
    },
    {
      title: '操作',
      key: 'actions',
      width: 250,
      fixed: 'right',
      render(row) {
        return h('div', { class: 'space-x-2 flex items-center' }, [ // items-center for vertical alignment
          h(NButton, { size: 'small', type: 'primary', ghost: true, onClick: () => onEdit(row) }, { default: () => '编辑' }),
          h(NButton, { size: 'small', type: 'info', ghost: true, onClick: () => onSchedule(row) }, { default: () => '排课' }),
          h(NButton, { size: 'small', type: 'error', ghost: true, onClick: () => onDelete(row) }, { default: () => '删除' })
        ]);
      },
    },
  ];
};

const handleDelete = (rowData) => {
  dialog.warning({
    title: '确认删除',
    content: `您确定要删除干部 "${rowData.name}" 吗？此操作不可恢复。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await cadreStore.deleteCadre(rowData.id);
        message.success(`干部 ${rowData.name} 已删除`);
        // cadreStore.allCadres 会变化，watch 会触发 filterAndPaginateData
      } catch (error) {
        message.error('删除失败，请重试');
        console.error("删除失败:", error);
      }
    }
  });
};

const columns = ref(createColumns({
  onEdit: openEditModal,
  onSchedule: openScheduleModal,
  onDelete: handleDelete,
}));

// --- Data Fetching, Filtering, Pagination, Sorting ---
const searchTerm = ref('');
const filterLevel = ref([]);
const filterCadreType = ref([]);

const paginationConfig = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  // prefix: ({ itemCount }) => `共 ${itemCount} 条` // NDataTable 自带 itemCount 显示
});

const currentSortKey = ref(null);
const currentSortOrder = ref(false); // false | "ascend" | "descend"

const filteredSortedDataCache = ref([]);
const paginatedData = ref([]);

let searchDebounceTimer = null;
const handleSearchDebounced = () => {
    clearTimeout(searchDebounceTimer);
    searchDebounceTimer = setTimeout(() => {
        handleSearch();
    }, 300); // 300ms 延迟
};

const handleSearch = () => {
  paginationConfig.value.page = 1;
  filterAndPaginateData();
};

const applyTableFilters = () => {
  paginationConfig.value.page = 1;
  filterAndPaginateData();
};

const filterAndPaginateData = () => {
  if (cadreStore.isLoading || unitStore.loading || universityStore.loading) {
    // 如果依赖数据仍在加载，可以暂时不处理或显示加载状态
    // paginatedData.value = [];
    // return;
  }
  let result = [...cadreStore.allCadres];

  // Search term filter
  if (searchTerm.value) {
    const term = searchTerm.value.toLowerCase();
    result = result.filter(item =>
      item.name?.toLowerCase().includes(term) ||
      (item.unitId && unitNameMap.value[item.unitId]?.toLowerCase().includes(term))
    );
  }

  // Level filter
  if (filterLevel.value && filterLevel.value.length > 0) {
    result = result.filter(item => filterLevel.value.includes(item.level));
  }

  // Cadre Type filter
  if (filterCadreType.value && filterCadreType.value.length > 0) {
    result = result.filter(item => filterCadreType.value.includes(item.cadreType));
  }

  // Sorting
  if (currentSortKey.value && currentSortOrder.value) {
    result.sort((a, b) => {
      let valA = a[currentSortKey.value];
      let valB = b[currentSortKey.value];
      
      // 如果是按 unitId 排序，但希望按单位名称排序，需要特殊处理
      if (currentSortKey.value === 'unitId') {
        valA = unitNameMap.value[valA] || '';
        valB = unitNameMap.value[valB] || '';
      }
      // 更多自定义排序逻辑可以加在这里，例如日期、数字等

      let comparison = 0;
      if (valA == null && valB != null) comparison = -1; // nulls first or last?
      else if (valA != null && valB == null) comparison = 1;
      else if (valA == null && valB == null) comparison = 0;
      else {
        if (typeof valA === 'number' && typeof valB === 'number') {
          comparison = valA - valB;
        } else if (valA instanceof Date && valB instanceof Date) { // birthDate is timestamp
          comparison = valA.getTime() - valB.getTime();
        } else if (typeof valA === 'string' && typeof valB === 'string') {
          comparison = valA.localeCompare(valB, 'zh-Hans-CN');
        } else {
            if (valA > valB) comparison = 1;
            else if (valA < valB) comparison = -1;
        }
      }
      return currentSortOrder.value === 'descend' ? comparison * -1 : comparison;
    });
  }
  
  filteredSortedDataCache.value = result;
  paginationConfig.value.itemCount = result.length;
  applyPagination();
};

const applyPagination = () => {
  if (paginationConfig.value.itemCount === 0) {
    paginatedData.value = [];
    return;
  }
  // 确保当前页码不超过总页数
  const totalPages = Math.ceil(paginationConfig.value.itemCount / paginationConfig.value.pageSize);
  if (paginationConfig.value.page > totalPages && totalPages > 0) {
    paginationConfig.value.page = totalPages;
  } else if (paginationConfig.value.itemCount === 0) {
     paginationConfig.value.page = 1;
  }

  const start = (paginationConfig.value.page - 1) * paginationConfig.value.pageSize;
  const end = start + paginationConfig.value.pageSize;
  paginatedData.value = filteredSortedDataCache.value.slice(start, end);
};

const handlePageChange = (page) => {
  paginationConfig.value.page = page;
  applyPagination();
};

const handlePageSizeChange = (pageSize) => {
  paginationConfig.value.pageSize = pageSize;
  paginationConfig.value.page = 1; // 通常切换每页数量时回到第一页
  applyPagination();
};

const handleSorterChange = (sorter) => {
  if (sorter && sorter.columnKey && sorter.order) {
    currentSortKey.value = sorter.columnKey;
    currentSortOrder.value = sorter.order;
  } else { // 清除排序
    currentSortKey.value = null;
    currentSortOrder.value = false;
  }
  paginationConfig.value.page = 1; // 排序改变，回到第一页
  filterAndPaginateData(); // 重新筛选和排序，然后分页
};


onMounted(async () => {
  // 加载顺序：先加载单位和高校这些基础数据，干部数据可能依赖它们进行映射或模拟生成
  // 使用 Promise.all 等待所有基础数据加载完成
  cadreStore.isLoading = true; // 手动设置一个总的加载状态
  try {
    await Promise.all([
      (unitStore.allUnits.length === 0 ? unitStore.fetchUnits() : Promise.resolve()),
      (universityStore.rawUniversities.length === 0 ? universityStore.fetchUniversities() : Promise.resolve())
    ]);
    // 确保干部数据在单位和高校数据加载后获取，因为 cadreStore 的 mock 可能依赖它们
    if (cadreStore.allCadres.length === 0) {
      await cadreStore.fetchCadres();
    }
  } catch (error) {
    console.error("Error during initial data fetch:", error);
    message.error("初始化数据加载失败");
  } finally {
    cadreStore.isLoading = false; // cadreStore 内部的 isLoading 也会被 fetchCadres 设置
    filterAndPaginateData(); // 初始加载和筛选数据
  }
});

// 监听干部数据变化（例如增删改后），重新筛选和分页
watch(() => cadreStore.allCadres, () => {
  filterAndPaginateData();
}, { deep: true });

// 也监听单位和高校数据变化，如果它们变了，可能需要刷新显示和筛选
watch(() => unitStore.allUnits, () => {
  filterAndPaginateData(); // 单位名称映射会更新
}, { deep: true });

watch(() => universityStore.rawUniversities, () => {
  filterAndPaginateData(); // 高校名称映射会更新
}, { deep: true });


// --- CSV Export Functionality (Placeholder) ---
const handleExportCsv = () => {
  if (!dataTableInstRef.value) {
    message.error('表格实例未准备好，请稍后再试');
    return;
  }
  if (paginatedData.value.length === 0) {
    message.warning('当前没有数据可以导出');
    return;
  }
  // 实际的导出逻辑会比较复杂，可能需要将 paginatedData (或者 filteredSortedDataCache) 转换为 CSV 格式并下载
  // dataTableInstRef.value.downloadCsv({ fileName: `干部信息表_第${paginationConfig.value.page}页.csv` }); // NaiveUI 可能不直接支持
  message.success(`模拟导出当前页数据为 CSV (共 ${paginatedData.value.length} 条)`);
};

</script>