<template>
  <div class="min-h-screen bg-slate-50">
    <div class="container mx-auto px-4 py-6">
      <section class="mb-8">
        <h2 class="text-2xl font-bold mb-4 text-slate-700">数据总览</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
          <n-card class="bg-white shadow-sm hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-500 text-sm">授课计划总数</p>
                <p class="text-3xl font-bold text-blue-600">{{ statistics.teachingPlans }}</p>
              </div>
              <div class="bg-blue-100 p-3 rounded-full">
                <n-icon size="24" class="text-blue-500"><BookOutline /></n-icon>
              </div>
            </div>
            <div class="mt-2 flex items-center text-sm">
              <n-icon class="text-green-500 mr-1"><TrendingUpOutline /></n-icon>
              <span class="text-green-500">{{ statistics.teachingPlansGrowth }}%</span>
              <span class="text-gray-500 ml-1">较上月</span>
            </div>
          </n-card>
          <n-card class="bg-white shadow-sm hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-500 text-sm">干部总数</p>
                <p class="text-3xl font-bold text-purple-600">{{ statistics.cadres }}</p>
              </div>
              <div class="bg-purple-100 p-3 rounded-full">
                <n-icon size="24" class="text-purple-500"><PeopleOutline /></n-icon>
              </div>
            </div>
            <div class="mt-2 flex items-center text-sm">
              <n-icon class="text-green-500 mr-1"><TrendingUpOutline /></n-icon>
              <span class="text-green-500">{{ statistics.cadresGrowth }}%</span>
              <span class="text-gray-500 ml-1">较上月</span>
            </div>
          </n-card>
          <n-card class="bg-white shadow-sm hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-500 text-sm">中央单位数</p>
                <p class="text-3xl font-bold text-amber-600">{{ statistics.centralUnits }}</p>
              </div>
              <div class="bg-amber-100 p-3 rounded-full">
                <n-icon size="24" class="text-amber-500"><BusinessOutline /></n-icon>
              </div>
            </div>
            <div class="mt-2 flex items-center text-sm">
              <n-icon class="text-green-500 mr-1"><TrendingUpOutline /></n-icon>
              <span class="text-green-500">{{ statistics.centralUnitsGrowth }}%</span>
              <span class="text-gray-500 ml-1">较上月</span>
            </div>
          </n-card>
          <n-card class="bg-white shadow-sm hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-500 text-sm">授课高校数</p>
                <p class="text-3xl font-bold text-green-600">{{ statistics.universities }}</p>
              </div>
              <div class="bg-green-100 p-3 rounded-full">
                <n-icon size="24" class="text-green-500"><SchoolOutline /></n-icon>
              </div>
            </div>
            <div class="mt-2 flex items-center text-sm">
              <n-icon class="text-green-500 mr-1"><TrendingUpOutline /></n-icon>
              <span class="text-green-500">{{ statistics.universitiesGrowth }}%</span>
              <span class="text-gray-500 ml-1">较上月</span>
            </div>
          </n-card>
          <n-card class="bg-white shadow-sm hover:shadow-md transition-shadow">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-500 text-sm">厅局级干部</p>
                <p class="text-3xl font-bold text-teal-600">{{ statistics.departmentLevelCadres }}</p>
              </div>
              <div class="bg-teal-100 p-3 rounded-full">
                <n-icon size="24" class="text-teal-500"><PodiumOutline /></n-icon>
              </div>
            </div>
             <div class="mt-2 flex items-center text-sm">
              <span class="text-gray-500 ml-1">当前在册</span>
            </div>
          </n-card>
        </div>
      </section>

      <section class="mb-8">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-2xl font-bold text-slate-700">本周上课信息</h2>
        </div>
        <n-card class="bg-white shadow-sm" content-style="padding: 0;">
          <div v-if="weeklyClassData.length > 0" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 p-4">
            <n-card
              v-for="(classItem, index) in weeklyClassData"
              :key="index"
              size="small"
              hoverable
              class="rounded-lg shadow-md hover:shadow-xl transition-all duration-300 ease-in-out"
              :style="{ borderTop: `4px solid ${statusMap[classItem.status]?.color || '#e0e0e0'}` }"
            >
              <template #header>
                <div class="flex justify-between items-start">
                  <h3 class="text-base font-semibold text-slate-700 leading-tight pr-2">{{ classItem.name }}</h3>
                  <n-tag :type="statusMap[classItem.status]?.tagType || 'default'" size="tiny" round class="ml-auto flex-shrink-0 mt-0.5">
                    {{ statusMap[classItem.status]?.text || classItem.status }}
                  </n-tag>
                </div>
              </template>

              <div class="space-y-2 text-sm text-slate-600 pt-1">
                <p class="flex items-center">
                  <n-icon size="16" :component="PersonOutline" class="mr-2 text-slate-400 flex-shrink-0" />
                  <span class="font-medium mr-1">授课人:</span> <n-text>{{ classItem.teacherName }} ({{ classItem.teacherLevel }})</n-text>
                </p>
                <p class="flex items-center">
                  <n-icon size="16" :component="SchoolOutline" class="mr-2 text-slate-400 flex-shrink-0" />
                  <span class="font-medium mr-1">高校:</span> <n-text depth="2">{{ classItem.university }}</n-text>
                </p>
                <p class="flex items-center">
                  <n-icon size="16" :component="BusinessSharp" class="mr-2 text-slate-400 flex-shrink-0" />
                  <span class="font-medium mr-1">单位:</span> <n-text depth="2">{{ classItem.unit }}</n-text>
                </p>
                <n-divider class="my-1.5" />
                <p class="flex items-center">
                  <n-icon size="16" :component="CalendarOutline" class="mr-2 text-slate-400 flex-shrink-0" />
                  <span class="font-medium mr-1">时间:</span> <n-text depth="2">{{ classItem.time }}</n-text>
                </p>
                <p class="flex items-center">
                  <n-icon size="16" :component="LocationOutline" class="mr-2 text-slate-400 flex-shrink-0" />
                  <span class="font-medium mr-1">地点:</span> <n-text depth="2">{{ classItem.location }}</n-text>
                </p>
                <p class="flex items-center">
                  <n-icon size="16" :component="PeopleOutline" class="mr-2 text-slate-400 flex-shrink-0" />
                  <span class="font-medium mr-1">人数:</span> <n-text depth="2">{{ classItem.participants }} 人</n-text>
                </p>
              </div>
            </n-card>
          </div>
          <div v-else class="text-center py-16">
            <n-empty description="本周暂无课程安排" size="huge">
              <template #icon>
                <n-icon :component="CalendarOutline" />
              </template>
            </n-empty>
          </div>
        </n-card>
      </section>

      <section class="mb-8">
        <h2 class="text-2xl font-bold mb-4 text-slate-700">授课计划分析</h2>
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <n-card class="bg-white shadow-sm col-span-1 lg:col-span-2">
             <h3 class="text-lg font-semibold mb-2 text-slate-600">月度授课量（计划 vs 完成）</h3>
            <div class="h-80" ref="yearlyPlanChartRef"></div>
          </n-card>
          <n-card class="bg-white shadow-sm">
            <h3 class="text-lg font-semibold mb-4 text-slate-600">季度计划完成率</h3>
            <div class="space-y-4">
              <div v-for="(item, index) in planCompletionData" :key="index">
                <div class="flex justify-between mb-1">
                  <span class="text-sm text-gray-600">{{ item.name }}</span>
                  <span class="text-sm font-medium" :style="{ color: item.color }">{{ item.value }}%</span>
                </div>
                <n-progress
                  type="line"
                  :percentage="item.value"
                  :color="item.color"
                  :height="8"
                  :border-radius="4"
                  :show-indicator="false"
                />
              </div>
            </div>
          </n-card>
          <n-card class="bg-white shadow-sm col-span-1 lg:col-span-3 mt-4">
             <h3 class="text-lg font-semibold mb-2 text-slate-600">学期授课计划状态分布</h3>
            <div class="h-80" ref="teachingSemesterPlanChartRef"></div>
          </n-card>
        </div>
      </section>

      <section class="mb-8">
        <h2 class="text-2xl font-bold mb-4 text-slate-700">干部数据深度分析</h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <n-card class="bg-white shadow-sm">
            <h3 class="text-lg font-semibold mb-2 text-slate-600">干部级别分布</h3>
            <div class="h-80" ref="cadreLevelDistributionChartRef"></div>
          </n-card>
          <n-card class="bg-white shadow-sm">
            <h3 class="text-lg font-semibold mb-2 text-slate-600">干部学历构成</h3>
            <div class="h-80" ref="cadreEducationChartRef"></div>
          </n-card>
           <n-card class="bg-white shadow-sm">
            <h3 class="text-lg font-semibold mb-2 text-slate-600">干部年龄结构</h3>
            <div class="h-80" ref="cadreAgeDistributionChartRef"></div>
          </n-card>
          <n-card class="bg-white shadow-sm">
            <h3 class="text-lg font-semibold mb-2 text-slate-600">热门拟授课方向</h3>
            <div class="h-80" ref="teachingAreaPopularityChartRef"></div>
          </n-card>
          <n-card class="bg-white shadow-sm">
             <h3 class="text-lg font-semibold mb-2 text-slate-600">培训状态分布</h3>
            <div class="h-80" ref="trainingStatusChartRef"></div>
          </n-card>
          <n-card class="bg-white shadow-sm">
            <h3 class="text-lg font-semibold mb-2 text-slate-600">培训效果评估</h3>
            <div class="h-80" ref="trainingEffectivenessChartRef"></div>
          </n-card>
        </div>
      </section>

      <section class="mb-8">
        <h2 class="text-2xl font-bold mb-4 text-slate-700">中央单位与合作分析</h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <n-card class="bg-white shadow-sm">
                <h3 class="text-lg font-semibold mb-2 text-slate-600">各单位培训人数</h3>
                <div class="h-80" ref="distributionChartRef"></div>
            </n-card>
            <n-card class="bg-white shadow-sm">
                <h3 class="text-lg font-semibold mb-2 text-slate-600">中央单位类型分布</h3>
                <div class="h-80" ref="unitTypeDistributionChartRef"></div>
            </n-card>
            <n-card class="bg-white shadow-sm">
                <h3 class="text-lg font-semibold mb-2 text-slate-600">合作高校类型分布</h3>
                <div class="h-80" ref="universityDistributionChartRef"></div>
            </n-card>
            <n-card class="bg-white shadow-sm">
                <h3 class="text-lg font-semibold mb-2 text-slate-600">合作高校地域分布</h3>
                <div class="h-80" ref="universityLocationChartRef"></div>
            </n-card>
             <n-card class="bg-white shadow-sm lg:col-span-2">
                <h3 class="text-lg font-semibold mb-2 text-slate-600">高校授课满意度</h3>
                <div class="h-80" ref="satisfactionChartRef"></div>
            </n-card>
        </div>
      </section>

      <section class="mb-8">
        <h2 class="text-2xl font-bold mb-4 text-slate-700">综合趋势</h2>
        <div class="grid grid-cols-1 gap-4">
          <n-card class="bg-white shadow-sm">
            <h3 class="text-lg font-semibold mb-2 text-slate-600">授课计划与干部培训趋势</h3>
            <div class="h-80" ref="trendChartRef"></div>
          </n-card>
        </div>
      </section>
    </div>

    <n-layout-footer class="bg-slate-100 shadow-inner py-4 px-6 text-center text-gray-500 border-t border-slate-200">
      <p>综合管理系统数据大屏 © {{ new Date().getFullYear() }}</p>
    </n-layout-footer>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, h, shallowRef } from 'vue';
import * as echarts from 'echarts';
import {
  BookOutline, PeopleOutline, BusinessOutline, SchoolOutline, TrendingUpOutline,
  PodiumOutline, LocationOutline, StatsChartOutline, PersonAddOutline, BusinessSharp,
  NotificationsOutline, SettingsOutline, // Retained if needed elsewhere
  CalendarOutline, PersonOutline, CheckmarkCircleOutline, InformationCircleOutline, BanOutline, TimeOutline, ChevronForward
} from '@vicons/ionicons5';
import { NTag, NProgress, NIcon, NButton, NCard, NLayoutFooter, NText, NEmpty, NDivider } from 'naive-ui';

// 图表引用
const yearlyPlanChartRef = ref(null);
const trendChartRef = ref(null);
const distributionChartRef = ref(null);
const trainingStatusChartRef = ref(null);
const trainingEffectivenessChartRef = ref(null);
const universityDistributionChartRef = ref(null);
const satisfactionChartRef = ref(null);
const cadreLevelDistributionChartRef = ref(null);
const cadreEducationChartRef = ref(null);
const teachingAreaPopularityChartRef = ref(null);
const unitTypeDistributionChartRef = ref(null);
const universityLocationChartRef = ref(null);
const cadreAgeDistributionChartRef = ref(null);
const teachingSemesterPlanChartRef = ref(null);

// 统计数据
const statistics = reactive({
  teachingPlans: 156, teachingPlansGrowth: 12,
  cadres: 573, cadresGrowth: 5,
  centralUnits: 42, centralUnitsGrowth: 2,
  universities: 86, universitiesGrowth: 3,
  departmentLevelCadres: 350,
  centrallyManagedCadres: 223,
  teachingPlansThisSemester: 25,
});

// 用户下拉菜单选项 (保留)
const userOptions = [
  { label: '个人资料', key: 'profile' },
  { label: '设置', key: 'settings' },
  { label: '退出登录', key: 'logout' }
];

const genericNames = ["张老师", "李老师", "王老师", "赵老师", "钱老师", "孙老师", "周老师", "吴老师", "郑老师", "冯老师"];

// 干部信息模拟数据 (匿名化)
const cadresData = ref([
  { name: genericNames[0], gender: '男', birthDate: '1955-07', nativePlace: '安徽定远', level: '中管干部', position: '原国务院总理', cadreType: '中管干部', unitAffiliation: '国务院办公厅', highestEducation: '博士研究生', highestDegree: '经济学博士', expertise: '经济、法律', teachingDirections: ['宏观经济形势', '国家治理现代化'], intendedUniversities: ['北京大学', '清华大学', '中国人民大学'], personnelChange: '退休', notes: '经验丰富' },
  { name: genericNames[1], gender: '男', birthDate: '1957-07', nativePlace: '福建福州', level: '中管干部', position: '公安部部长', cadreType: '中管干部', unitAffiliation: '公安部', highestEducation: '大学本科', highestDegree: '法学学士', expertise: '公安工作、社会治理', teachingDirections: ['国家安全战略', '社会治安防控体系建设'], intendedUniversities: ['中国人民公安大学', '中国政法大学'], personnelChange: '', notes: '' },
  { name: genericNames[2], gender: '男', birthDate: '1970-05', nativePlace: '山东济南', level: '厅局级干部', position: '某司司长', cadreType: '厅局级干部', unitAffiliation: '国家发改委', highestEducation: '博士研究生', highestDegree: '管理学博士', expertise: '产业政策、区域发展', teachingDirections: ['新兴产业发展趋势', '区域协调发展', '数字经济'], intendedUniversities: ['北京大学', '复旦大学'], personnelChange: '', notes: '学术背景强' },
  { name: genericNames[3], gender: '女', birthDate: '1978-11', nativePlace: '江苏南京', level: '厅局级干部', position: '某处处长', cadreType: '厅局级干部', unitAffiliation: '教育部', highestEducation: '硕士研究生', highestDegree: '教育学硕士', expertise: '教育政策、高等教育管理', teachingDirections: ['教育体制改革', '高校学科建设', '宏观经济形势'], intendedUniversities: ['北京师范大学', '华东师范大学'], personnelChange: '新任命', notes: '' },
  { name: genericNames[4], gender: '男', birthDate: '1965-02', nativePlace: '浙江杭州', level: '厅局级干部', position: '某局副局长', cadreType: '厅局级干部', unitAffiliation: '国家市场监督管理总局', highestEducation: '大学本科', highestDegree: '工学学士', expertise: '市场监管、质量管理', teachingDirections: ['食品安全监管', '知识产权保护', '国家治理现代化'], intendedUniversities: ['浙江大学', '上海交通大学'], personnelChange: '', notes: '' },
  { name: genericNames[5], gender: '女', birthDate: '1982-09', nativePlace: '四川成都', level: '厅局级干部', position: '某研究院院长', cadreType: '厅局级干部', unitAffiliation: '中国社会科学院', highestEducation: '博士研究生', highestDegree: '哲学博士', expertise: '社会学、文化研究', teachingDirections: ['社会变迁与发展', '文化自信与传播', '新兴产业发展趋势'], intendedUniversities: ['四川大学', '南京大学'], personnelChange: '', notes: '' },
]);

// 中央单位模拟数据
const centralUnitsData = ref([
  { unitCode: '001', receptionCode: 'R001', unitName: '中共中央组织部', unitAbbreviation: '中组部', unitType: '党中央部门', unitLevel: '正部级', contactPerson: '赵组织', contactPhone: '010-xxxxxxx', notes: '' },
  { unitCode: '002', receptionCode: 'R002', unitName: '国家发展和改革委员会', unitAbbreviation: '国家发改委', unitType: '国务院组成部门', unitLevel: '正部级', contactPerson: '钱发展', contactPhone: '010-yyyyyyy', notes: '负责宏观经济调控' },
  { unitCode: '003', receptionCode: 'R003', unitName: '教育部', unitAbbreviation: '教育部', unitType: '国务院组成部门', unitLevel: '正部级', contactPerson: '孙教育', contactPhone: '010-zzzzzzz', notes: '' },
  { unitCode: '004', receptionCode: 'R004', unitName: '科学技术部', unitAbbreviation: '科技部', unitType: '国务院组成部门', unitLevel: '正部级', contactPerson: '李科技', contactPhone: '010-aaaaaaa', notes: '' },
  { unitCode: '005', receptionCode: 'R005', unitName: '中共中央宣传部', unitAbbreviation: '中宣部', unitType: '党中央部门', unitLevel: '正部级', contactPerson: '周宣传', contactPhone: '010-bbbbbbb', notes: '' },
]);

// 授课高校模拟数据
const universitiesData = ref([
  { schoolName: '北京大学', schoolCode: '10001', supervisingDepartment: '教育部', location: '北京市', isCentralDirectlyAffiliated: true, contactDepartment: '教务处', contactPosition: '处长', contactName: '周教务', contactPhone: '010-pkupku', notes: '顶尖学府' },
  { schoolName: '清华大学', schoolCode: '10003', supervisingDepartment: '教育部', location: '北京市', isCentralDirectlyAffiliated: true, contactDepartment: '科研院', contactPosition: '院长', contactName: '吴科研', contactPhone: '010-thuthu', notes: '' },
  { schoolName: '中国人民大学', schoolCode: '10002', supervisingDepartment: '教育部', location: '北京市', isCentralDirectlyAffiliated: true, contactDepartment: '培训学院', contactPosition: '主任', contactName: '郑培训', contactPhone: '010-rucruc', notes: '人文社科强校' },
  { schoolName: '复旦大学', schoolCode: '10246', supervisingDepartment: '教育部', location: '上海市', isCentralDirectlyAffiliated: true, contactDepartment: '继续教育学院', contactPosition: '副院长', contactName: '陈继续', contactPhone: '021-fdfdfd', notes: '' },
  { schoolName: '上海交通大学', schoolCode: '10248', supervisingDepartment: '教育部', location: '上海市', isCentralDirectlyAffiliated: true, contactDepartment: '干部培训中心', contactPosition: '主任', contactName: '冯培训', contactPhone: '021-sjtusjtu', notes: '' },
  { schoolName: '浙江大学', schoolCode: '10335', supervisingDepartment: '教育部', location: '浙江省', isCentralDirectlyAffiliated: true, contactDepartment: '管理学院EDP', contactPosition: '项目主管', contactName: '何发展', contactPhone: '0571-zdzdzd', notes: '' },
  { schoolName: '南京大学', schoolCode: '10284', supervisingDepartment: '教育部', location: '江苏省', isCentralDirectlyAffiliated: true, contactDepartment: '政府管理学院', contactPosition: '书记', contactName: '许管理', contactPhone: '025-nfnfnf', notes: '' },
  { schoolName: '北京师范大学', schoolCode: '10027', supervisingDepartment: '教育部', location: '北京市', isCentralDirectlyAffiliated: true, contactDepartment: '教育学部', contactPosition: '部长', contactName: '刘教育', contactPhone: '010-bsdbsd', notes: '' },
]);

// 本周上课数据 (for Card Grid, 匿名化)
const weeklyClassData = ref([
  { name: '党建理论与实践', teacherName: genericNames[2], teacherLevel: '厅局级干部', unit: '中央组织部', university: '北京大学', time: '2025-05-20 09:00-12:00', location: '人文学院报告厅', participants: 120, status: 'upcoming' },
  { name: '国家经济政策解读', teacherName: genericNames[0], teacherLevel: '中管干部', unit: '国家发改委', university: '清华大学', time: '2025-05-21 14:00-17:00', location: '经管学院报告厅', participants: 85, status: 'upcoming' },
  { name: '领导力提升培训', teacherName: genericNames[3], teacherLevel: '厅局级干部', unit: '中央党校', university: '中国人民大学', time: '2025-05-22 09:00-16:00', location: '中国人民大学明德楼', participants: 60, status: 'confirmed' },
  { name: '数字化转型与政府治理', teacherName: genericNames[4], teacherLevel: '厅局级干部', unit: '国家行政学院', university: '北京师范大学', time: '2025-05-23 09:00-12:00', location: '北京师范大学科技楼', participants: 75, status: 'ongoing' },
  { name: '国际形势与外交政策', teacherName: genericNames[1], teacherLevel: '中管干部', unit: '外交部', university: '外交学院', time: '2025-05-18 14:00-17:00', location: '外交学院国际会议中心', participants: 90, status: 'completed' },
  { name: '公共危机管理', teacherName: genericNames[5], teacherLevel: '厅局级干部', unit: '应急管理部', university: '清华大学', time: '2025-05-24 09:00-12:00', location: '公共管理学院', participants: 50, status: 'cancelled' },
]);

// Status mapping for Cards and Tags
const statusMap = {
  upcoming: { tagType: 'info', text: '即将开始', icon: shallowRef(TimeOutline), color: '#2080f0' },
  ongoing: { tagType: 'success', text: '进行中', icon: shallowRef(InformationCircleOutline), color: '#18a058' },
  completed: { tagType: 'default', text: '已完成', icon: shallowRef(CheckmarkCircleOutline), color: '#B0B0B0' },
  confirmed: { tagType: 'warning', text: '已确认', icon: shallowRef(CalendarOutline), color: '#f0a020' },
  cancelled: { tagType: 'error', text: '已取消', icon: shallowRef(BanOutline), color: '#d03050' }
};


// 计划完成率数据
const planCompletionData = [
  { name: '第一季度', value: 100, color: '#18a058' },
  { name: '第二季度', value: 85, color: '#2080f0' },
  { name: '第三季度', value: 45, color: '#f0a020' },
  { name: '第四季度', value: 10, color: '#d03050' }
];

// 用户操作处理
const handleUserAction = (key) => {
  console.log('User action:', key);
};

// 初始化图表
onMounted(() => {
  const chartRefs = {
    yearlyPlanChartRef, trendChartRef, distributionChartRef, trainingStatusChartRef,
    trainingEffectivenessChartRef, universityDistributionChartRef, satisfactionChartRef,
    cadreLevelDistributionChartRef, cadreEducationChartRef, teachingAreaPopularityChartRef,
    unitTypeDistributionChartRef, universityLocationChartRef, cadreAgeDistributionChartRef,
    teachingSemesterPlanChartRef
  };
  const charts = {};

  function initChart(refName, optionGenerator) {
    if (chartRefs[refName] && chartRefs[refName].value) {
      // Ensure ECharts are initialized on a visible element or after it becomes visible
      // For simplicity here, we assume it's ready
      const chartInstance = echarts.init(chartRefs[refName].value);
      chartInstance.setOption(optionGenerator());
      charts[refName] = chartInstance;
    } else {
      console.warn(`Chart ref ${refName} not found or value is null.`);
    }
  }

  // --- ECharts 初始化逻辑 (与之前相同，保持不变) ---
  // 1. 年度授课计划完成情况图表
  initChart('yearlyPlanChartRef', () => ({
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    legend: { data: ['计划课程', '已完成课程'], top: 5 },
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
    xAxis: { type: 'category', data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'] },
    yAxis: { type: 'value', name: '课程数' },
    series: [
      { name: '计划课程', type: 'bar', data: [12, 8, 15, 18, 20, 22, 25, 18, 20, 15, 12, 10], itemStyle: { color: '#2080f0' }, barGap: 0 },
      { name: '已完成课程', type: 'bar', data: [10, 7, 15, 16, 18, 20, 0, 0, 0, 0, 0, 0], itemStyle: { color: '#18a058' } }
    ]
  }));

  // 2. 数据趋势分析图表 (授课计划与干部培训趋势)
  initChart('trendChartRef', () => ({
    tooltip: { trigger: 'axis' },
    legend: { data: ['授课计划数', '干部培训人次'], top: 5 },
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
    xAxis: { type: 'category', boundaryGap: false, data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'] },
    yAxis: { type: 'value', name: '数量/人次' },
    series: [
      { name: '授课计划数', type: 'line', smooth: true, data: [120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330], itemStyle: { color: '#2080f0' }, areaStyle: { color: new echarts.graphic.LinearGradient(0,0,0,1, [{offset:0, color:'rgba(32,128,240,0.4)'},{offset:1, color:'rgba(32,128,240,0.1)'}])}},
      { name: '干部培训人次', type: 'line', smooth: true, data: [220, 182, 191, 234, 290, 330, 310, 123, 442, 321, 90, 149], itemStyle: { color: '#18a058' }, areaStyle: { color: new echarts.graphic.LinearGradient(0,0,0,1, [{offset:0, color:'rgba(24,160,88,0.4)'},{offset:1, color:'rgba(24,160,88,0.1)'}])}}
    ]
  }));

  // 3. 各单位培训人数分布图表
  initChart('distributionChartRef', () => ({
    tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c}人 ({d}%)' },
    legend: { orient: 'vertical', left: 10, top: 20, data: ['中央组织部', '中央宣传部', '中央政法委', '国家发改委', '教育部', '其他单位'] },
    series: [{
      name: '培训人数', type: 'pie', radius: '60%', center: ['60%', '50%'],
      data: [
        { value: 148, name: '中央组织部' }, { value: 120, name: '中央宣传部' },
        { value: 95, name: '中央政法委' }, { value: 85, name: '国家发改委' },
        { value: 75, name: '教育部' }, { value: 50, name: '其他单位' }
      ],
      emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0,0,0,0.5)' } },
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272']
    }]
  }));

  // 4. 培训状态分布图表
  initChart('trainingStatusChartRef', () => ({
    tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c}人 ({d}%)' },
    legend: { bottom: 10, left: 'center', data: ['已完成', '培训中', '待培训'] },
    series: [{
      name: '培训状态', type: 'pie', radius: ['45%', '70%'], avoidLabelOverlap: false,
      itemStyle: { borderRadius: 8, borderColor: '#fff', borderWidth: 2 },
      label: { show: false, position: 'center' },
      emphasis: { label: { show: true, fontSize: '16', fontWeight: 'bold' } },
      data: [
        { value: 235, name: '已完成', itemStyle: { color: '#18a058' } },
        { value: 180, name: '培训中', itemStyle: { color: '#2080f0' } },
        { value: 158, name: '待培训', itemStyle: { color: '#f0a020' } }
      ]
    }]
  }));

  // 5. 培训效果评估图表
  initChart('trainingEffectivenessChartRef', () => ({
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    legend: { data: ['优秀', '良好', '一般', '待改进'], top: 5 },
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
    xAxis: [{ type: 'category', data: ['理论知识', '实践能力', '领导力', '创新思维', '团队协作'] }],
    yAxis: [{ type: 'value', max: 100, name:'占比(%)' }],
    series: [
      { name: '优秀', type: 'bar', stack: 'total', emphasis: { focus: 'series' }, itemStyle: { color: '#18a058' }, data: [35, 25, 30, 20, 40] },
      { name: '良好', type: 'bar', stack: 'total', emphasis: { focus: 'series' }, itemStyle: { color: '#2080f0' }, data: [40, 45, 35, 35, 30] },
      { name: '一般', type: 'bar', stack: 'total', emphasis: { focus: 'series' }, itemStyle: { color: '#f0a020' }, data: [20, 20, 25, 30, 20] },
      { name: '待改进', type: 'bar', stack: 'total', emphasis: { focus: 'series' }, itemStyle: { color: '#d03050' }, data: [5, 10, 10, 15, 10] }
    ]
  }));

  // 6. 合作高校分布图表
  initChart('universityDistributionChartRef', () => ({
    tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c}所 ({d}%)' },
    legend: { orient: 'vertical', right: 10, top: 'center', data: ['985高校', '211高校', '普通本科', '专科院校', '其他'] },
    series: [{
      name: '高校类型', type: 'pie', radius: ['50%', '70%'], center: ['40%', '50%'], avoidLabelOverlap: false,
      label: { show: false, position: 'center' },
      emphasis: { label: { show: true, fontSize: '16', fontWeight: 'bold' } },
      data: [
        { value: 25, name: '985高校' }, { value: 30, name: '211高校' },
        { value: 20, name: '普通本科' }, { value: 8, name: '专科院校' }, { value: 3, name: '其他' }
      ],
      color: ['#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
    }]
  }));

  // 7. 高校授课满意度图表
  initChart('satisfactionChartRef', () => ({
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    grid: { left: '3%', right: '10%', bottom: '3%', containLabel: true },
    xAxis: { type: 'value', max: 100, name: '满意度(%)' },
    yAxis: { type: 'category', data: ['北京大学', '清华大学', '中国人民大学', '北京师范大学', '复旦大学', '上海交通大学', '浙江大学', '南京大学'], axisLabel:{ interval: 0 } },
    series: [{
      name: '满意度', type: 'bar', barWidth: '60%',
      data: [
        { value: 98, itemStyle: { color: '#18a058' } }, { value: 96, itemStyle: { color: '#18a058' } },
        { value: 94, itemStyle: { color: '#18a058' } }, { value: 92, itemStyle: { color: '#2080f0' } },
        { value: 90, itemStyle: { color: '#2080f0' } }, { value: 88, itemStyle: { color: '#2080f0' } },
        { value: 85, itemStyle: { color: '#f0a020' } }, { value: 82, itemStyle: { color: '#f0a020' } }
      ],
      label: { show: true, position: 'right', formatter: '{c}%' }
    }]
  }));

  // 8. 干部级别分布
  initChart('cadreLevelDistributionChartRef', () => {
    const cadreLevelData = cadresData.value.reduce((acc, cadre) => {
      acc[cadre.level] = (acc[cadre.level] || 0) + 1; return acc;
    }, {});
    return {
      tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c}人 ({d}%)' },
      legend: { orient: 'vertical', left: 10, top: 'center', data: Object.keys(cadreLevelData) },
      series: [{
        name: '干部级别', type: 'pie', radius: ['45%', '70%'], center: ['60%', '50%'], avoidLabelOverlap: false,
        itemStyle: { borderRadius: 8, borderColor: '#fff', borderWidth: 2 },
        label: { show: true, formatter: '{b}\n{d}%', position: 'outside'},
        emphasis: { label: { show: true, fontSize: 14, fontWeight: 'bold' } },
        data: Object.entries(cadreLevelData).map(([name, value]) => ({
          name, value, itemStyle: { color: name === '中管干部' ? '#d76a2c' : (name === '厅局级干部' ? '#5470c6' : '#91cc75') }
        })),
      }]
    };
  });

  // 9. 干部学历构成
  initChart('cadreEducationChartRef', () => {
    const educationData = cadresData.value.reduce((acc, cadre) => {
      const edu = cadre.highestEducation || '其他'; acc[edu] = (acc[edu] || 0) + 1; return acc;
    }, {});
    const sortedEducation = Object.entries(educationData).sort((a,b) => b[1] - a[1]);
    return {
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      grid: { left: '3%', right: '4%', bottom: '10%', containLabel: true },
      xAxis: { type: 'category', data: sortedEducation.map(item => item[0]), axisLabel: { interval: 0, rotate: 30 } },
      yAxis: { type: 'value', name: '人数' },
      series: [{ name: '人数', type: 'bar', data: sortedEducation.map(item => item[1]), itemStyle: { color: '#fac858' }, barWidth: '50%', label: { show: true, position: 'top' } }]
    };
  });

  // 10. 干部热门拟授课方向
  initChart('teachingAreaPopularityChartRef', () => {
    const areaData = cadresData.value.flatMap(cadre => cadre.teachingDirections || [])
      .reduce((acc, direction) => { acc[direction] = (acc[direction] || 0) + 1; return acc; }, {});
    const sortedAreas = Object.entries(areaData).sort((a,b) => a[1] - b[1]).slice(-7); // Top 7
    return {
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      grid: { left: '3%', right: '10%', bottom: '3%', top:'10%', containLabel: true },
      xAxis: { type: 'value', name: '提及次数' },
      yAxis: { type: 'category', data: sortedAreas.map(item => item[0]), axisLabel: { interval: 0, width: 70, overflow: 'truncate'} },
      series: [{ name: '提及次数', type: 'bar', data: sortedAreas.map(item => item[1]), itemStyle: { color: new echarts.graphic.LinearGradient(0,0,1,0, [{offset:0,color:'#83bff6'},{offset:1,color:'#188df0'}]) }, label: { show: true, position: 'right' } }]
    };
  });

  // 11. 单位类型分布
  initChart('unitTypeDistributionChartRef', () => {
    const unitTypeData = centralUnitsData.value.reduce((acc, unit) => {
      acc[unit.unitType] = (acc[unit.unitType] || 0) + 1; return acc;
    }, {});
    return {
      tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c}个 ({d}%)' },
      legend: { orient: 'horizontal', bottom: 10, data: Object.keys(unitTypeData) },
      series: [{
        name: '单位类型', type: 'pie', radius: '60%', center: ['50%', '45%'],
        data: Object.entries(unitTypeData).map(([name, value]) => ({ name, value })),
        emphasis: { itemStyle: { shadowBlur: 10, shadowOffsetX: 0, shadowColor: 'rgba(0,0,0,0.5)' } },
        color: ['#ee6666', '#fc8452', '#9a60b4', '#ea7ccc', '#FFDB5C', '#9FE080']
      }]
    };
  });

  // 12. 合作高校地域分布
  initChart('universityLocationChartRef', () => {
    const locationData = universitiesData.value.reduce((acc, uni) => {
      acc[uni.location] = (acc[uni.location] || 0) + 1; return acc;
    }, {});
    const sortedLocations = Object.entries(locationData).sort((a,b) => b[1] - a[1]).slice(0,7); // Top 7
    return {
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      grid: { left: '3%', right: '4%', bottom: '10%', top:'15%', containLabel: true },
      xAxis: { type: 'category', data: sortedLocations.map(item => item[0]), axisLabel: { interval: 0, rotate: 30 } },
      yAxis: { type: 'value', name: '高校数量' },
      series: [{ name: '高校数量', type: 'bar', data: sortedLocations.map(item => item[1]), itemStyle: { color: '#73c0de' }, barWidth: '60%', label: { show: true, position: 'top'} }]
    };
  });

  // 13. 干部年龄结构
  initChart('cadreAgeDistributionChartRef', () => {
    const currentYear = new Date().getFullYear();
    const ageData = cadresData.value.map(cadre => currentYear - parseInt(cadre.birthDate.substring(0,4)))
      .reduce((acc, age) => {
        let group = age < 30 ? '30岁以下' : age <= 40 ? '30-40岁' : age <= 50 ? '41-50岁' : age <= 60 ? '51-60岁' : '60岁以上';
        acc[group] = (acc[group] || 0) + 1; return acc;
      }, {});
    const ageOrder = ['30岁以下', '30-40岁', '41-50岁', '51-60岁', '60岁以上'];
    return {
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
      xAxis: { type: 'category', data: ageOrder },
      yAxis: { type: 'value', name: '人数' },
      series: [{ name: '人数', type: 'bar', data: ageOrder.map(group => ageData[group] || 0), itemStyle: { color: '#9FE080' }, barWidth: '50%', label: { show: true, position: 'top' } }]
    };
  });

  // 14. 按学期授课计划状态分布
  initChart('teachingSemesterPlanChartRef', () => {
    const semesterPlans = [ 
        { semester: '2025春季', status: '计划中', count: 15 }, { semester: '2025春季', status: '已确认', count: 20 }, { semester: '2025春季', status: '已完成', count: 18 }, { semester: '2025春季', status: '已取消', count: 2 },
        { semester: '2025秋季', status: '计划中', count: 25 }, { semester: '2025秋季', status: '已确认', count: 10 }, { semester: '2025秋季', status: '已完成', count: 5 },
        { semester: '2024秋季', status: '已完成', count: 30 }, { semester: '2024秋季', status: '已确认', count: 12 },
    ];
    const semesters = [...new Set(semesterPlans.map(p => p.semester))].sort((a,b) => b.localeCompare(a)); 
    const statuses = ['计划中', '已确认', '已完成', '已取消'];
    const statusColors = { '计划中': '#f0a020', '已确认': '#2080f0', '已完成': '#18a058', '已取消': '#d03050' };
    return {
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      legend: { data: statuses, top: 5 },
      grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
      xAxis: { type: 'category', data: semesters },
      yAxis: { type: 'value', name: '计划数量' },
      series: statuses.map(status => ({
        name: status, type: 'bar', stack: '总量', emphasis: { focus: 'series' },
        itemStyle: { color: statusColors[status] }, label: { show: true, formatter: (params) => params.value > 0 ? params.value : '' },
        data: semesters.map(sem => semesterPlans.find(p => p.semester === sem && p.status === status)?.count || 0)
      }))
    };
  });
  // --- ECharts 初始化逻辑结束 ---

  window.addEventListener('resize', () => {
    Object.values(charts).forEach(chart => {
      if (chart && typeof chart.resize === 'function') {
        chart.resize();
      }
    });
  });

});
</script>

<style scoped>
.h-80 {
  height: 20rem; /* 320px */
}
/* Tailwind JIT should pick up these utility classes, but if not, define them here or in global styles */
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse))); /* 8px */
    margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.my-1\.5 {
    margin-top: 0.375rem; /* 6px */
    margin-bottom: 0.375rem; /* 6px */
}
.mr-1\.5 {
    margin-right: 0.375rem; /* 6px */
}
.flex-shrink-0 {
    flex-shrink: 0;
}
.pt-1 {
  padding-top: 0.25rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.leading-tight {
  line-height: 1.25;
}
.ml-auto {
  margin-left: auto;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}

/* Custom scrollbar for Webkit browsers if needed for specific elements */
/* ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-thumb {
  background: #cbd5e1; 
  border-radius: 3px;
}
::-webkit-scrollbar-thumb:hover {
  background: #a0aec0; 
} */
</style>