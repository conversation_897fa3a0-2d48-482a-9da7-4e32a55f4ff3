<template>
  <n-form
    ref="formRef"
    :model="formValue"
    :rules="rules"
    label-placement="left"
    label-width="100px" 
    require-mark-placement="right-hanging"
  >
    <n-grid :cols="24" :x-gap="24">
      <n-form-item-gi :span="12" label="授课干部" path="cadreId">
        <n-select
          v-model:value="formValue.cadreId"
          placeholder="请选择授课干部"
          :options="cadreOptions"
          :loading="cadreStore.isLoading"
          filterable
          clearable
          @update:value="handleCadreChange"
          :disabled="isCadreFixed" 
        />
      </n-form-item-gi>

      <n-form-item-gi :span="12" label="授课高校" path="universityId">
        <n-select
          v-model:value="formValue.universityId"
          placeholder="请选择授课高校"
          :options="universityOptionsGrouped"
          :loading="universityStore.loading || loadingCadreDetails"
          filterable
          clearable
          :disabled="!formValue.cadreId"
        >
          <template #empty>
            {{ formValue.cadreId ? '加载中或该干部无意向高校...' : '请先选择干部' }}
          </template>
        </n-select>
      </n-form-item-gi>

      <n-form-item-gi :span="12" label="拟授课方向" path="selectedTeachingDirection">
        <n-select
          v-model:value="formValue.selectedTeachingDirection"
          placeholder="请从干部拟授课方向中选择"
          :options="proposedDirectionOptions"
          :loading="loadingCadreDetails"
          clearable
          :disabled="!formValue.cadreId || proposedDirectionOptions.length === 0"
        >
          <template #empty>
            {{ formValue.cadreId ? (loadingCadreDetails ? '加载干部方向...' : '该干部无拟授课方向') : '请先选择干部' }}
          </template>
        </n-select>
      </n-form-item-gi>

      <n-form-item-gi :span="12" label="上课日期" path="classDate">
        <n-date-picker v-model:value="formValue.classDate" type="date" placeholder="请选择上课日期" style="width: 100%;" />
      </n-form-item-gi>

      <n-form-item-gi :span="12" label="上课时间" path="classTime">
        <n-time-picker
          v-model:value="formValue.classTime"
          placeholder="请选择上课时间"
          format="HH:mm"
          :actions="['now', 'clear']"
          style="width: 100%;"
        />
      </n-form-item-gi>

      <n-form-item-gi :span="12" label="授课地点" path="location">
        <n-input v-model:value="formValue.location" placeholder="请输入授课地点 (校区、楼名、教室号)" />
      </n-form-item-gi>

      <n-form-item-gi :span="12" label="计划学时" path="plannedHours">
        <n-input-number v-model:value="formValue.plannedHours" placeholder="请输入计划学时" :min="0.5" :step="0.5" style="width: 100%;">
          <template #suffix>学时</template>
        </n-input-number>
      </n-form-item-gi>
      
      <n-form-item-gi :span="12" label="授课学期" path="teachingSemester">
        <n-select
            v-model:value="formValue.teachingSemester"
            placeholder="请选择授课学期"
            :options="semesterOptions" 
            clearable
        />
      </n-form-item-gi>

      <n-form-item-gi :span="12" label="授课状态" path="teachingStatus">
        <n-select
          v-model:value="formValue.teachingStatus"
          placeholder="请选择授课状态"
          :options="teachingStatusOptions"
          clearable
        />
      </n-form-item-gi>

      <n-form-item-gi :span="24" label="备注" path="remarks">
        <n-input
          v-model:value="formValue.remarks"
          type="textarea"
          placeholder="请输入备注信息 (可选)"
          :autosize="{ minRows: 2, maxRows: 4 }"
        />
      </n-form-item-gi>
    </n-grid>

    <div class="flex justify-end mt-6">
      <n-button @click="handleCancel" class="mr-4">取消</n-button>
      <n-button type="primary" @click="handleValidateAndSubmit" :loading="isSubmitting">
        {{ isEditing ? '保存更新' : '创建计划' }}
      </n-button>
    </div>
  </n-form>
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue';
import {
  NForm, NFormItemGi, NInput, NSelect, NDatePicker, NTimePicker,
  NInputNumber, NButton, NGrid, useMessage
} from 'naive-ui';
import { useCadreStore } from '@/stores/cadreStore';
import { useUniversityStore } from '@/stores/universityStore';
// 确保 planOptions.js 和 cadreOptions.js 路径正确并已导出所需选项
import { teachingStatusOptions, semesterOptions, courseSeriesOptions as staticCourseSeriesOptions /*保留课程系列用于其他地方或移除*/ } from './planOptions';
// 导入干部“拟授课方向”的全局定义，用于映射value到label
import { teachingDirectionOptions as globalTeachingDirectionOptions } from '@/views/cadres/cadreOptions';


const message = useMessage();
const formRef = ref(null);
const cadreStore = useCadreStore();
const universityStore = useUniversityStore();

const props = defineProps({
  initialData: {
    type: Object,
    default: () => null
  }
});

const emit = defineEmits(['submit', 'cancel']);

const isSubmitting = ref(false);
const isEditing = computed(() => !!props.initialData?.id);
const isCadreFixed = computed(() => !!props.initialData?.cadreId && !isEditing.value);

const getDefaultFormValue = () => ({
  id: null,
  cadreId: null,
  universityId: null,
  selectedTeachingDirection: null, // 新字段，用于存储选择的拟授课方向的 *value*
  // courseName: '', // 移除，其功能由 selectedTeachingDirection 替代
  classDate: null,
  classTime: null,
  location: '',
  plannedHours: null,
  teachingStatus: 'planned',
  teachingSemester: null,
  remarks: '',
  // 保留 courseSeries 如果你还想用它，或者如果后端模型需要区分，否则可以移除
  // 如果 selectedTeachingDirection 就是 courseSeries 的新含义，确保后端也知道
  courseSeries: null, // 视情况保留或移除，或用 selectedTeachingDirection 的值填充
});

const formValue = ref(getDefaultFormValue());
const selectedCadreDetails = ref(null);
const loadingCadreDetails = ref(false);

// 将全局的拟授课方向选项转换为 Map，方便通过value查找label
const teachingDirectionMap = computed(() => {
  return new Map(globalTeachingDirectionOptions.map(opt => [opt.value, opt.label]));
});

const cadreOptions = computed(() =>
  cadreStore.allCadres.map(c => ({ label: c.name, value: c.id }))
);

const universityOptionsGrouped = computed(() => {
  // ... (此部分逻辑与之前相同，保持不变)
  if (!formValue.value.cadreId ) {
    return universityStore.rawUniversities.map(u => ({ label: u.schoolName, value: u.id }));
  }
  if (!selectedCadreDetails.value && loadingCadreDetails.value) { return []; }
  if (!selectedCadreDetails.value) { return universityStore.rawUniversities.map(u => ({ label: u.schoolName, value: u.id }));}
  const intendedUniIds = [selectedCadreDetails.value.intendedUniversity1, selectedCadreDetails.value.intendedUniversity2, selectedCadreDetails.value.intendedUniversity3,].filter(id => id != null);
  const intendedOptions = [];
  const otherOptions = [];
  universityStore.rawUniversities.forEach(u => {
    const option = { label: u.schoolName, value: u.id };
    if (intendedUniIds.includes(u.id)) { intendedOptions.push(option); } 
    else { otherOptions.push(option); }
  });
  const groups = [];
  if (intendedOptions.length > 0) { groups.push({ type: 'group', label: '干部意向高校', key: 'intended', children: intendedOptions }); }
  groups.push({ type: 'group', label: '其他高校', key: 'other', children: otherOptions });
  return groups;
});

// 新的计算属性，为“拟授课方向”下拉框提供选项
const proposedDirectionOptions = computed(() => {
  if (selectedCadreDetails.value && selectedCadreDetails.value.proposedTeachingDirections) {
    return selectedCadreDetails.value.proposedTeachingDirections.map(directionValue => ({
      label: teachingDirectionMap.value.get(directionValue) || directionValue, // 使用映射的label，如果找不到则用value本身
      value: directionValue
    }));
  }
  return [];
});

watch(() => props.initialData, async (newData) => {
  if (newData) {
    formValue.value = {
      ...getDefaultFormValue(),
      id: newData.id || null,
      cadreId: newData.cadreId || null,
      universityId: newData.universityId || null,
      // 如果旧数据中有 courseName 或 courseSeries 代表主题，需要映射到 selectedTeachingDirection
      // 假设 selectedTeachingDirection 是新字段，编辑时从 newData.selectedTeachingDirection 或 newData.courseSeries(如果改名) 读取
      selectedTeachingDirection: newData.selectedTeachingDirection || newData.courseSeries || null, 
      courseSeries: newData.courseSeries || null, // 如果还保留 courseSeries 字段的话
      classDate: newData.classDate ? Number(newData.classDate) : null,
      classTime: newData.classTime ? Number(newData.classTime) : null,
      location: newData.location || '',
      plannedHours: newData.plannedHours != null ? Number(newData.plannedHours) : null,
      teachingStatus: newData.teachingStatus || 'planned',
      teachingSemester: newData.teachingSemester || null,
      remarks: newData.remarks || '',
    };
    if (formValue.value.cadreId) {
      await loadCadreDetails(formValue.value.cadreId);
    }
  } else {
    resetFormInternal();
  }
}, { immediate: true, deep: true });

async function loadCadreDetails(cadreId) {
  if (!cadreId) {
    selectedCadreDetails.value = null;
    return;
  }
  loadingCadreDetails.value = true;
  try {
    let cadre = cadreStore.allCadres.find(c => c.id === cadreId);
    if (cadre) { // 确保 cadre 对象包含 proposedTeachingDirections
      selectedCadreDetails.value = cadre;
    } else {
      selectedCadreDetails.value = null;
      // message.error('未在干部列表中找到该干部信息');
    }
  } catch (error) {
    message.error('加载干部详细信息失败');
    selectedCadreDetails.value = null;
  } finally {
    loadingCadreDetails.value = false;
  }
}

async function handleCadreChange(cadreId) {
  formValue.value.universityId = null;
  formValue.value.selectedTeachingDirection = null; // 重置选择的拟授课方向
  // formValue.value.courseSeries = null; // 如果 courseSeries 就是 selectedTeachingDirection，也重置
  await loadCadreDetails(cadreId);
}

const rules = {
  cadreId: { required: true, message: '请选择授课干部', trigger: 'change', type: 'string' },
  universityId: { required: true, message: '请选择授课高校', trigger: 'change', type: 'number' },
  selectedTeachingDirection: { required: true, message: '请选择拟授课方向', trigger: 'change' }, // 新的验证规则
  // courseName 规则移除
  classDate: { type: 'number', required: true, message: '请选择上课日期', trigger: 'change' },
  classTime: { type: 'number', required: true, message: '请选择上课时间', trigger: 'change' },
  location: { required: true, message: '请输入授课地点', trigger: ['input', 'blur'] },
  plannedHours: { 
    type: 'number', required: true, message: '请输入计划学时', trigger: ['blur', 'change'],
    validator: (rule, value) => (value != null && value > 0) || new Error('学时必须是大于0的数字')
  },
  teachingStatus: { required: true, message: '请选择授课状态', trigger: 'change' },
  teachingSemester: { required: true, message: '请选择授课学期', trigger: 'change' }
};

const handleValidateAndSubmit = async (e) => {
  e.preventDefault();
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      isSubmitting.value = true;
      try {
        const dataToSubmit = { ...formValue.value };
        // 如果后端期望的字段名是 courseSeries 而不是 selectedTeachingDirection, 在这里转换
        // 例如: dataToSubmit.courseSeries = dataToSubmit.selectedTeachingDirection;
        // delete dataToSubmit.selectedTeachingDirection;
        
        // 或者，如果你决定在 formValue 中直接使用 courseSeries 来存储选择的方向值，
        // 那么 getDefaultFormValue, watch, handleCadreChange 中的相关字段名也应统一为 courseSeries

        emit('submit', dataToSubmit);
      } catch (error) {
        message.error(isEditing.value ? '更新失败' : '创建失败');
      } finally {
        isSubmitting.value = false;
      }
    } else {
      message.error('请检查表单填写');
    }
  });
};

const resetFormInternal = () => {
  formValue.value = getDefaultFormValue();
  selectedCadreDetails.value = null;
  if (formRef.value) {
    formRef.value.restoreValidation();
  }
};

const resetForm = () => { resetFormInternal(); };
const handleCancel = () => { emit('cancel'); };

defineExpose({ resetForm });

onMounted(async () => {
  const fetchCadresPromise = (cadreStore.allCadres.length === 0 && !cadreStore.isLoading) 
    ? cadreStore.fetchCadres() : Promise.resolve();
  const fetchUniversitiesPromise = (universityStore.rawUniversities.length === 0 && !universityStore.loading) 
    ? universityStore.fetchUniversities() : Promise.resolve();
  await Promise.all([fetchCadresPromise, fetchUniversitiesPromise]);
  if (formValue.value.cadreId && !selectedCadreDetails.value) {
     await loadCadreDetails(formValue.value.cadreId);
  }
});
</script>