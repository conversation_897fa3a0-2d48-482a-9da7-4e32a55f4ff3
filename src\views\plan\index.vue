<template>
  <div class="px-4 sm:px-8 md:px-16 lg:px-48 pt-4 w-full">
    <div class="flex flex-col">
      <n-card :title="'授课计划管理'">
        <template #header-extra>
          <n-button :render-icon="renderAddIcon" type="primary" @click="openCreateModal">
            新增授课计划
          </n-button>
        </template>

        <div class="flex flex-wrap gap-4 mt-4 items-center">
          <div>
            <n-input
              v-model:value="searchTerm"
              placeholder="搜索干部、高校、方向、地点"
              clearable
              @update:value="debouncedApplyFilters"
              style="min-width: 280px;"
            >
              <template #prefix>
                <n-icon :component="SearchOutline" />
              </template>
            </n-input>
          </div>
          <div class="min-w-48">
            <n-select
              v-model:value="filterTeachingStatus"
              placeholder="筛选授课状态"
              :options="teachingStatusOptions" multiple
              filterable
              clearable
              @update:value="applyFilters"
            />
          </div>
           <div class="min-w-48">
            <n-select
              v-model:value="filterSemester"
              placeholder="筛选授课学期"
              :options="semesterOptions" multiple
              filterable
              clearable
              @update:value="applyFilters"
            />
          </div>
          <div class="flex-1" />
        </div>

        <div class="mt-6">
          <n-data-table
            :columns="columns"
            :data="paginatedData"
            :loading="planStore.isLoading || cadreStore.isLoading || universityStore.isLoading"
            :pagination="paginationConfig"
            :row-key="row => row.id"
            remote 
            @update:page="handlePageChange"
            @update:page-size="handlePageSizeChange"
            @update:sorter="handleSorterChange"
            :scroll-x="2000" class="min-w-full"
          />
        </div>
      </n-card>
    </div>

    <n-modal
      v-model:show="showModal"
      preset="card"
      :style="{ width: '900px' }"
      :title="modalTitle"
      size="huge"
      :bordered="false"
      :segmented="{ content: 'soft', footer: 'soft' }"
      closable
      @after-leave="handleModalClose"
    >
      <plan-form 
        ref="planFormRef" 
        :initial-data="editingPlan" 
        @submit="handleFormSubmit" 
        @cancel="closeModal" 
      />
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h, computed, watch } from 'vue';
import {
  NButton, NInput, NIcon, NSelect, NDataTable, NModal, useMessage, NSpace, 
  NTag, NPopconfirm, NTooltip, NCard
} from 'naive-ui';
import { SearchOutline, AddOutline as AddIcon, PencilOutline as EditIcon, TrashOutline as DeleteIcon, EyeOutline as ViewIcon } from '@vicons/ionicons5';
import { useRouter } from 'vue-router';
import PlanForm from './PlanForm.vue';
import { usePlanStore } from '@/stores/planStore';
import { useCadreStore } from '@/stores/cadreStore';
import { useUniversityStore } from '@/stores/universityStore';
// 从干部模块的选项中导入全局的“拟授课方向”定义
import { teachingDirectionOptions as globalTeachingDirectionOptions } from '@/views/cadres/cadreOptions';
// 从本模块的选项中导入授课状态、学期等
import { teachingStatusOptions, semesterOptions } from './planOptions'; // courseSeriesOptions 不再直接用于此列显示

const message = useMessage();
const router = useRouter();
const planStore = usePlanStore();
const cadreStore = useCadreStore();
const universityStore = useUniversityStore();

const searchTerm = ref('');
const filterTeachingStatus = ref([]);
const filterSemester = ref([]);
const showModal = ref(false);
const modalTitle = ref('新增授课计划');
const editingPlan = ref(null);
const planFormRef = ref(null);

const filteredAndSortedPlans = ref([]);
const paginatedData = ref([]);

// --- 用于表格显示的映射 ---
const cadreNameMap = computed(() => Object.fromEntries(cadreStore.allCadres.map(c => [c.id, c.name])));
const universityNameMap = computed(() => Object.fromEntries(universityStore.rawUniversities.map(u => [u.id, u.schoolName])));
const statusDisplayMap = computed(() => Object.fromEntries(teachingStatusOptions.map(opt => [opt.value, { label: opt.label, type: opt.type || 'default' }])));
const semesterDisplayMap = computed(() => Object.fromEntries(semesterOptions.map(opt => [opt.value, opt.label])));
// 新增：用于显示“拟授课方向”的标签
const teachingDirectionDisplayMap = computed(() => 
  new Map(globalTeachingDirectionOptions.map(opt => [opt.value, opt.label]))
);


// --- 分页与排序状态 ---
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
});
const currentSorter = ref(null);

const paginationConfig = computed(() => ({ ...pagination }));

// --- 表格列定义 ---
const createColumns = ({ handleEdit, handleDeletePlan, handleViewCadre }) => [
  { 
    title: '序号', 
    key: 'tableIndex',
    width: 60, 
    align: 'center',
    render: (_, index) => h('span', (pagination.page - 1) * pagination.pageSize + index + 1) 
  },
  { 
    title: '授课干部', 
    key: 'cadreId', 
    sorter: true, 
    width: 150, 
    ellipsis: { tooltip: true },
    render: row => cadreNameMap.value[row.cadreId] || `未知 (ID:${row.cadreId})` 
  },
  { 
    title: '授课高校', 
    key: 'universityId', 
    sorter: true, 
    width: 180, 
    ellipsis: { tooltip: true },
    render: row => universityNameMap.value[row.universityId] || `未知 (ID:${row.universityId})` 
  },
  { 
    title: '拟授课方向/主题', // 修改列标题
    key: 'courseSeries', // 对应 PlanForm 中存储选定方向的字段 (或 courseSeries 如果你用它)
    width: 220, 
    sorter: true,
    ellipsis: { tooltip: true },
     render: row => {
      // 从 Map 中查找 label，如果找不到，则显示原始值，再找不到则显示 '-'

      return teachingDirectionDisplayMap.value.get(row.courseSeries) || row.courseSeries || '-';
    }
    // 假设 Plan 对象中存储该方向值的字段是 selectedTeachingDirection
    // 如果你在 PlanForm 和 planStore 中决定用 courseSeries 存储这个值，这里也用 row.courseSeries
  },
  // { title: '课程名称/主题', key: 'courseName', ... }, // 移除此列
  { 
    title: '授课日期', 
    key: 'classDate', 
    width: 130, 
    sorter: true,
    render: row => row.classDate ? new Date(row.classDate).toLocaleDateString() : '待定'
  },
   { 
    title: '授课时间', 
    key: 'classTime', 
    width: 100, 
    render: row => row.classTime ? new Date(row.classTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: false }) : '待定'
  },
  { 
    title: '授课地点', 
    key: 'location', 
    width: 180, 
    ellipsis: { tooltip: true } 
  },
  { 
    title: '学时', 
    key: 'plannedHours', 
    width: 80, 
    sorter: true, 
    align: 'center'
  },
  { 
    title: '授课学期', 
    key: 'teachingSemester', 
    width: 150, 
    sorter: true,
    render: row => semesterDisplayMap.value[row.teachingSemester] || row.teachingSemester 
  },
  {
    title: '授课状态', 
    key: 'teachingStatus', 
    width: 110, 
    sorter: true, 
    align: 'center',
    fixed: 'right',
    render(row) {
      const statusInfo = statusDisplayMap.value[row.teachingStatus] || { label: row.teachingStatus, type: 'default' };
      return h(NTag, { type: statusInfo.type, bordered: false, size: 'small' }, { default: () => statusInfo.label });
    }
  },
  {
    title: '操作', 
    key: 'actions', 
    width: 240,
    align: 'center', 
    fixed: 'right',
    render(row) {
      // ... (操作按钮逻辑与之前相同)
      return h(NSpace, { justify: 'center' }, { default: () => [
        h(NButton, { size: 'small', type: 'primary', ghost: true, onClick: () => handleEdit(row), renderIcon: () => h(NIcon, null, { default: () => h(EditIcon) }) }, { default: () => '编辑' }),
        h(NButton, { size: 'small', type: 'info', ghost: true, onClick: () => handleViewCadre(row.cadreId), renderIcon: () => h(NIcon, null, { default: () => h(ViewIcon) }) }, { default: () => '干部' }),
        h(NPopconfirm, 
          { onPositiveClick: () => handleDeletePlan(row.id), positiveText: "确认删除", negativeText: "取消" },
          { 
            trigger: () => h(NButton, { size: 'small', type: 'error', ghost: true, renderIcon: () => h(NIcon, null, { default: () => h(DeleteIcon) }) }, { default: () => '删除' }),
            default: () => `确定要删除 “${cadreNameMap.value[row.cadreId] || '该干部'}” 的这条授课计划吗？`
          }
        ),
      ]});
    }
  },
];

const columns = ref(createColumns({
  handleEdit: (plan) => { /* ...与之前相同... */ 
    modalTitle.value = `编辑 “${cadreNameMap.value[plan.cadreId] || '干部'}” 的授课计划`; 
    editingPlan.value = JSON.parse(JSON.stringify(plan));
    showModal.value = true; 
  },
  handleDeletePlan: async (planId) => { /* ...与之前相同... */ 
    try {
      await planStore.deletePlan(planId);
      message.success(`授课计划删除成功！`);
    } catch (error) {
      message.error('删除失败，请重试。');
    }
  },
  handleViewCadre: (cadreId) => { /* ...与之前相同... */ 
    if (cadreId) {
      router.push({ name: 'Cadres', params: { highlightId: cadreId } }); 
      message.info(`正在尝试跳转到干部 (ID: ${cadreId}) 的信息页...`);
    } else {
      message.warning('未关联有效干部。')
    }
  }
}));

// --- 模态框处理 --- (与之前相同)
const renderAddIcon = () => h(NIcon, null, { default: () => h(AddIcon) });
const openCreateModal = () => { /* ... */ 
  modalTitle.value = '新增授课计划'; 
  editingPlan.value = null; 
  showModal.value = true; 
};
const closeModal = () => { showModal.value = false; };
const handleModalClose = () => { /* ... */ 
  editingPlan.value = null; 
  if (planFormRef.value && typeof planFormRef.value.resetForm === 'function') {
    planFormRef.value.resetForm();
  }
};

// --- 数据处理与过滤 ---
function processPlans() {
  let dataToProcess = [...planStore.allPlans];

  // 1. 筛选
   if (searchTerm.value) {
    const term = searchTerm.value.toLowerCase();
    dataToProcess = dataToProcess.filter(plan =>
      (cadreNameMap.value[plan.cadreId] || '').toLowerCase().includes(term) ||
      (universityNameMap.value[plan.universityId] || '').toLowerCase().includes(term) ||
      // 修改搜索逻辑：基于 "拟授课方向" 的显示标签
      (teachingDirectionDisplayMap.value.get(plan.courseSeries) || '').toLowerCase().includes(term) || // <--- 修改这里
      (plan.location || '').toLowerCase().includes(term)
    );
  }
  if (filterTeachingStatus.value && filterTeachingStatus.value.length > 0) {
    dataToProcess = dataToProcess.filter(plan => filterTeachingStatus.value.includes(plan.teachingStatus));
  }
  if (filterSemester.value && filterSemester.value.length > 0) {
    dataToProcess = dataToProcess.filter(plan => filterSemester.value.includes(plan.teachingSemester));
  }

  // 2. 排序
  if (currentSorter.value && currentSorter.value.key && currentSorter.value.order) {
    const { key, order } = currentSorter.value;
    const isAsc = order === 'ascend';
    dataToProcess.sort((a, b) => {
      let valA = a[key];
      let valB = b[key];

      if (key === 'cadreId') { valA = cadreNameMap.value[valA] || ''; valB = cadreNameMap.value[valB] || ''; } 
      else if (key === 'universityId') { valA = universityNameMap.value[valA] || ''; valB = universityNameMap.value[valB] || ''; }
      // 修改排序逻辑：基于 "拟授课方向" 的显示标签
      else if (key === 'selectedTeachingDirection') { 
        valA = teachingDirectionDisplayMap.value.get(valA) || ''; 
        valB = teachingDirectionDisplayMap.value.get(valB) || ''; 
      }
      else if (key === 'classDate' || key === 'classTime' || key === 'plannedHours') { valA = Number(valA || 0); valB = Number(valB || 0); }
      else if (typeof valA === 'string' && typeof valB === 'string') { return isAsc ? valA.localeCompare(valB) : valB.localeCompare(valA); }
      
      if (valA < valB) return isAsc ? -1 : 1;
      if (valA > valB) return isAsc ? 1 : -1;
      return 0;
    });
  }
  
  filteredAndSortedPlans.value = dataToProcess;
  pagination.itemCount = dataToProcess.length;
  applyPagination();
}

function applyPagination() { /* ...与之前相同... */ 
  const totalPages = Math.ceil(pagination.itemCount / pagination.pageSize);
  if (pagination.page > totalPages && totalPages > 0) { pagination.page = totalPages; } 
  else if (pagination.itemCount === 0) { pagination.page = 1; }
  const start = (pagination.page - 1) * pagination.pageSize;
  const end = start + pagination.pageSize;
  paginatedData.value = filteredAndSortedPlans.value.slice(start, end);
}

// --- 事件处理 --- (大部分与之前相同)
let filterDebounceTimer = null;
const debouncedApplyFilters = () => { /* ... */ 
    clearTimeout(filterDebounceTimer);
    filterDebounceTimer = setTimeout(() => { applyFilters(); }, 300);
};
const applyFilters = () => { pagination.page = 1; processPlans(); };
const handlePageChange = (page) => { pagination.page = page; applyPagination(); };
const handlePageSizeChange = (pageSize) => { /* ... */
  pagination.pageSize = pageSize; pagination.page = 1; applyPagination();
};
const handleSorterChange = (sorter) => { /* ... */ 
  if (sorter && sorter.columnKey && sorter.order) { currentSorter.value = { key: sorter.columnKey, order: sorter.order }; } 
  else { currentSorter.value = null; }
  pagination.page = 1; processPlans(); 
};
const handleFormSubmit = async (formDataFromForm) => { /* ...与之前相同... */ 
  try {
    if (formDataFromForm.id) { await planStore.updatePlan(formDataFromForm); message.success('授课计划更新成功！'); } 
    else { await planStore.addPlan(formDataFromForm); message.success('新增授课计划成功！'); }
    closeModal();
  } catch (error) {
    message.error((formDataFromForm.id ? '更新' : '新增') + '授课计划失败，请重试。');
  }
};

// --- 生命周期与侦听 --- (与之前相同)
onMounted(async () => { /* ... */ 
  planStore.isLoading = true;
  try {
    await Promise.all([
      (cadreStore.allCadres.length === 0 ? cadreStore.fetchCadres() : Promise.resolve()),
      (universityStore.rawUniversities.length === 0 ? universityStore.fetchUniversities() : Promise.resolve())
    ]);
    await planStore.fetchPlans();
  } catch (error) {
    message.error("加载页面数据时出错。");
  } finally {
    planStore.isLoading = false;
  }
});
watch(() => planStore.allPlans, () => { processPlans(); }, { deep: true, immediate: true });
watch([() => cadreStore.allCadres, () => universityStore.rawUniversities], () => { processPlans(); }, { deep: true, immediate: true });

</script>