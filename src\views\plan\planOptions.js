// src/views/plan/planOptions.js

export const teachingStatusOptions = [
  { label: '计划中', value: 'planned', type: 'info' },
  { label: '进行中', value: 'ongoing', type: 'warning' },
  { label: '已完成', value: 'completed', type: 'success' },
  { label: '已取消', value: 'cancelled', type: 'error' },
  { label: '已调整', value: 'adjusted', type: 'default' },
];

export const semesterOptions = [
  { label: '2024-2025学年 第一学期', value: '2024-2025-1' },
  { label: '2024-2025学年 第二学期', value: '2024-2025-2' },
  { label: '2025-2026学年 第一学期', value: '2025-2026-1' },
  { label: '2025-2026学年 第二学期', value: '2025-2026-2' },
  // ...更多学期
];

export const courseSeriesOptions = [
  { label: '领导力与战略管理', value: 'leadership_strategy' },
  { label: '公共政策与治理', value: 'public_policy_governance' },
  { label: '经济发展与改革', value: 'economic_development_reform' },
  { label: '数字技术与创新', value: 'digital_tech_innovation' },
  { label: '法律法规与实务', value: 'law_practice' },
  { label: '人文素养与沟通', value: 'humanities_communication' },
  { label: '其他专题', value: 'other_topics' },
];

// 你之前的 PlanForm.vue 中还用到了 placeholderCadreOptions 和 placeholderUniversityOptions
// 但由于现在我们会从 cadreStore 和 universityStore 动态加载真实数据，
// 所以这些 placeholder 选项在新的 PlanForm.vue 中不再需要了。
// 在 index.vue 中，我们也用 cadreNameMap 和 universityNameMap 来显示真实名称。