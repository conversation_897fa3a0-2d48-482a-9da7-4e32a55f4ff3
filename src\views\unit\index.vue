<template>
  <div class="px-4 sm:px-8 md:px-16 lg:px-48 pt-4 w-full">
    <div class="flex flex-col">
      <n-card title="单位管理">
        <template #header-extra>
          <n-button :render-icon="renderAddIcon" type="primary" @click="unitStore.openTheCreateModal">
            新增单位
          </n-button>
        </template>

        <div class="flex flex-row flex-wrap mt-4 space-x-4">
          <div>
            <n-input :value="searchTerm" placeholder="按名称、简称、联系人搜索..." clearable style="min-width: 240px;"
              @update:value="handleSearchInput" @clear="clearSearch">
              <template #prefix>
                <n-icon :component="SearchOutline" />
              </template>
            </n-input>
          </div>
          <div class="min-w-48">
            <n-select :value="filterUnitType" placeholder="筛选单位类型" :options="unitStore.unitTypeFilterOptions" multiple
              filterable clearable @update:value="unitStore.updateFilterUnitType" />
          </div>
          <div class="min-w-48">
            <n-select :value="filterUnitLevel" placeholder="筛选单位级别" :options="unitStore.unitLevelFilterOptions" multiple
              filterable clearable @update:value="unitStore.updateFilterUnitLevel" />
          </div>
          <div class="flex-1" />
        </div>

        <div class="mt-6">
          <n-data-table :columns="columns" :data="unitStore.paginatedData" :loading="unitStore.loading"
            :pagination="unitStore.paginationConfig" :row-key="row => row.id" remote
            @update:page="unitStore.handlePageChange" @update:page-size="unitStore.handlePageSizeChange"
            @update:sorter="unitStore.handleSorterChange" class="min-w-full" :scroll-x="2320" />
        </div>
      </n-card>
    </div>

    <n-modal :show="showModal" preset="card" :style="{ width: '900px' }" :title="modalTitle" size="huge"
      :bordered="false" :segmented="{ content: 'soft', footer: 'soft' }" closable
      @update:show="val => !val && unitStore.closeTheModal()" @after-leave="handleModalAfterLeave">
      <unit-form ref="unitFormInstance" :initial-data="editingUnit" @submit="handleFormSubmit"
        @cancel="unitStore.closeTheModal" />
    </n-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, h, computed } from 'vue';
import { storeToRefs } from 'pinia';
import {
  NButton, NInput, NIcon, NSelect, NDataTable, NModal, useMessage, NSpace, NTag, NPopconfirm, NCard
} from 'naive-ui';
import { SearchOutline, AddOutline as AddIcon, PencilOutline as EditIcon, TrashOutline as DeleteIcon } from '@vicons/ionicons5';

import UnitForm from './UnitForm.vue';
// 确保 unitOptions.js 的路径正确，如果 store 中已引用，这里可以不直接引用
// import { unitTypeOptions, unitLevelOptions } from './unitOptions.js'; 

import { useUnitStore } from '@/stores/unitStore';

const message = useMessage();
const unitStore = useUnitStore();

// 从 store 中获取响应式状态
const {
  searchTerm,     // 用于 n-input v-model 或 :value
  filterUnitType, // 用于 n-select v-model 或 :value
  filterUnitLevel,// 用于 n-select v-model 或 :value
  showModal,
  modalTitle,
  editingUnit,
  // loading, // 改为 unitStore.loading
  // paginationConfig, // 改为 unitStore.paginationConfig
  // paginatedData // 改为 unitStore.paginatedData
} = storeToRefs(unitStore); // storeToRefs 用于解构 state 和 getters

const unitFormInstance = ref(null);

const renderAddIcon = () => h(NIcon, null, { default: () => h(AddIcon) });

const createColumns = ({ handleEdit, handleDeleteConfirmation }) => [
  {
    title: '序号',
    key: 'index',
    width: 60,
    render: (row, index) => {
      // 使用 store 中的 pagination 来计算序号，确保 pagination 是响应式的
      return h('span', (unitStore.pagination.page - 1) * unitStore.pagination.pageSize + index + 1);
    }
  },
  { title: '单位名称', key: 'name', sorter: true, ellipsis: { tooltip: true }, width: 150 },
  { title: '单位简称', key: 'shortName', sorter: true, ellipsis: { tooltip: true }, width: 120 },
  { title: '校准编号', key: 'calibrationCode', sorter: true, width: 150 },
  { title: '收件编号', key: 'receiptCode', sorter: true, width: 150 }, // 新增收件编号列
  {
    title: '单位类型', key: 'unitType', width: 120, sorter: true,
    // filterable: true, // 移除列筛选，统一使用外部 Select
    // filterOptions: unitStore.unitTypeFilterOptions, // 移除
    // filter: (value, row) => unitStore.filterUnitType.value.length === 0 || unitStore.filterUnitType.value.includes(row.unitType), // 移除
    render(row) {
      const type = unitStore.unitTypeFilterOptions.find(opt => opt.value === row.unitType);
      return type ? h(NTag, { type: 'info', bordered: false }, { default: () => type.label }) : '';
    }
  },
  {
    title: '单位级别', key: 'unitLevel', width: 120, sorter: true,
    // filterable: true, // 移除列筛选
    // filterOptions: unitStore.unitLevelFilterOptions, // 移除
    // filter: (value, row) => unitStore.filterUnitLevel.value.length === 0 || unitStore.filterUnitLevel.value.includes(row.unitLevel), // 移除
    render(row) {
      const level = unitStore.unitLevelFilterOptions.find(opt => opt.value === row.unitLevel);
      return level ? h(NTag, { type: 'success', bordered: false }, { default: () => level.label }) : '';
    }
  },
  { title: '联系人', key: 'contactPerson', sorter: true, width: 100 },
  { title: '座机', key: 'landlinePhone', width: 130 },
  { title: '手机', key: 'mobilePhone', width: 120 },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    align: 'center',
    fixed: 'right',
    render(row) {
      return h(NSpace, null, {
        default: () => [
          h(
            NButton,
            {
              size: 'small', type: 'primary', ghost: true,
              onClick: () => handleEdit(row),
              renderIcon: () => h(NIcon, null, { default: () => h(EditIcon) })
            },
            { default: () => '编辑' }
          ),
          h(
            NPopconfirm,
            {
              onPositiveClick: () => handleDeleteConfirmation(row),
              positiveText: "确认删除",
              negativeText: "取消"
            },
            {
              trigger: () => h(
                NButton,
                {
                  size: 'small', type: 'error', ghost: true,
                  renderIcon: () => h(NIcon, null, { default: () => h(DeleteIcon) })
                },
                { default: () => '删除' }
              ),
              default: () => '确定要删除这条记录吗？'
            }
          ),
        ],
      });
    },
  },
];

const columns = computed(() => createColumns({ // 将 columns 定义为 computed，以便响应 unitStore 中选项的变化
  handleEdit: (rowData) => {
    unitStore.openTheEditModal(rowData);
  },
  handleDeleteConfirmation: async (rowData) => {
    const msgKey = `delete-${rowData.id}`;
    message.loading(`正在删除单位 "${rowData.name}"...`, { key: msgKey, duration: 0 });
    const result = await unitStore.deleteUnit(rowData.id, rowData.name); // 调用 store action
    if (result.success) {
      message.success(result.message, { key: msgKey });
    } else {
      message.error(result.message || '删除失败', { key: msgKey });
    }
  },
}));

const handleFormSubmit = async (formData) => {
  const msgKey = 'form-submit';
  message.loading('正在提交...', { key: msgKey, duration: 0 });
  const result = await unitStore.submitUnit(formData);
  if (result.success) {
    message.success(result.message || (editingUnit.value && editingUnit.value.id ? '单位更新成功' : '单位新增成功'), { key: msgKey });
    // Modal 关闭和数据刷新由 store 的 submitUnit 内部的 fetchUnits 和 closeTheModal 处理
  } else {
    message.error(result.message || '操作失败，请重试', { key: msgKey });
  }
};

const handleModalAfterLeave = () => {
  unitStore.handleModalClosed(); // 通知 store modal 已完全关闭以重置 editingUnit
  if (unitFormInstance.value && typeof unitFormInstance.value.resetForm === 'function') {
    unitFormInstance.value.resetForm(); // 调用 UnitForm.vue 中的重置方法
  }
};

// 处理搜索输入，使用 store 的防抖方法
const handleSearchInput = (value) => {
  // 如果希望输入框内容立即响应，而不是等待防抖结束才更新 searchTerm
  // 可以考虑在 store 中维护一个临时的输入值，或者让 searchTerm 立即更新，但 fetchUnits 防抖
  // unitStore.searchTerm = value; // 立即更新输入框绑定的值（如果 searchTerm 是这样用的）
  unitStore.debouncedUpdateSearchTerm(value); // 调用防抖的action
};

const clearSearch = () => {
  unitStore.updateSearchTerm(''); // 直接更新并获取数据
};

onMounted(() => {
  unitStore.fetchUnits();
});

</script>

<style scoped>
.n-data-table {
  word-break: keep-all;
  /* 保持单词不断行 */
}

/* 确保在小屏幕上筛选区域能良好换行 */
.flex-wrap {
  flex-wrap: wrap;
}

.min-w-48 {
  min-width: 12rem;
  /* 约192px */
  margin-bottom: 0.5rem;
  /* 在换行时提供一些间距 */
}
</style>