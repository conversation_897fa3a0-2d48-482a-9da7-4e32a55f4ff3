<template>
  <n-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-placement="left"
    label-width="auto"
    require-mark-placement="right-hanging"
    class="py-4"
  >
    <n-grid :cols="2" :x-gap="24">
      <n-form-item-grid-item label="单位名称" path="name">
        <n-input v-model:value="formData.name" placeholder="请输入单位全称" />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="单位简称" path="shortName">
        <n-input v-model:value="formData.shortName" placeholder="请输入单位简称" />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="校准编号" path="calibrationCode">
        <n-input v-model:value="formData.calibrationCode" placeholder="请输入校准编号" />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="收件编号" path="receiptCode">
        <n-input v-model:value="formData.receiptCode" placeholder="请输入收件编号" />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="单位类型" path="unitType">
        <n-select
          v-model:value="formData.unitType"
          placeholder="请选择单位类型"
          :options="unitTypeOptions"
          filterable
        />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="单位级别" path="unitLevel">
        <n-select
          v-model:value="formData.unitLevel"
          placeholder="请选择单位级别"
          :options="unitLevelOptions"
          filterable
        />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="单位联系人" path="contactPerson">
        <n-input v-model:value="formData.contactPerson" placeholder="请输入单位联系人" />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="座机" path="landlinePhone">
        <n-input v-model:value="formData.landlinePhone" placeholder="请输入座机号码" />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="手机" path="mobilePhone">
        <n-input v-model:value="formData.mobilePhone" placeholder="请输入手机号码" />
      </n-form-item-grid-item>
      <n-form-item-grid-item :span="2" label="备注" path="remarks">
        <n-input
          v-model:value="formData.remarks"
          type="textarea"
          placeholder="请输入备注信息"
          :autosize="{ minRows: 3, maxRows: 5 }"
        />
      </n-form-item-grid-item>
    </n-grid>

    <div class="mt-6 flex justify-end space-x-4">
      <n-button @click="handleCancel">
        取消
      </n-button>
      <n-button type="primary" @click="handleSubmit">
        提交
      </n-button>
    </div>
  </n-form>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, onMounted } from 'vue';
import {
  NForm,
  NFormItem,
  NFormItemGridItem,
  NInput,
  NSelect,
  NButton,
  NGrid,
  useMessage
} from 'naive-ui';
import { unitTypeOptions, unitLevelOptions } from './unitOptions'; // Adjusted import path

const props = defineProps({
  initialData: {
    type: Object,
    default: null,
  },
});

const emits = defineEmits(['submit', 'cancel']);

const message = useMessage();
const formRef = ref(null);
const defaultFormValues = {
  id: null,
  name: '',
  shortName: '',
  calibrationCode: '',
  receiptCode: '',
  unitType: null,
  unitLevel: null,
  contactPerson: '',
  landlinePhone: '',
  mobilePhone: '',
  remarks: '',
};
const formData = ref({ ...defaultFormValues });

const rules = {
  name: {
    required: true,
    message: '请输入单位名称',
    trigger: ['input', 'blur'],
  },
  unitType: {
    required: true,
    message: '请选择单位类型',
    trigger: ['change', 'blur'],
  },
  // mobilePhone: {
  //   validator: (rule, value) => {
  //     if (value && !/^1[3-9]\d{9}$/.test(value)) {
  //       return new Error('请输入正确的手机号码');
  //     }
  //     return true;
  //   },
  //   trigger: ['input', 'blur'],
  // },
};

watch(() => props.initialData, (newData) => {
  if (newData) {
    formData.value = { ...defaultFormValues, ...newData };
  } else {
    formData.value = { ...defaultFormValues };
  }
}, { immediate: true, deep: true });


const handleSubmit = (e) => {
  e.preventDefault();
  formRef.value?.validate((errors) => {
    if (!errors) {
      emits('submit', { ...formData.value });
    } else {
      message.error('请检查表单填写是否正确');
    }
  });
};

const handleCancel = () => {
  emits('cancel');
};

const resetForm = () => {
  formData.value = { ...defaultFormValues };
  formRef.value?.restoreValidation();
};

defineExpose({
  resetForm,
});

onMounted(() => {
  if (props.initialData) {
    formData.value = { ...defaultFormValues, ...props.initialData };
  }
});

</script>