<template>
  <n-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-placement="left"
    label-width="auto"
    require-mark-placement="right-hanging"
    class="py-4"
  >
    <n-grid :cols="2" :x-gap="24">
      <n-form-item-grid-item label="学校名称" path="schoolName">
        <n-input v-model:value="formData.schoolName" placeholder="请输入学校全称" />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="学校标准码" path="schoolCode">
        <n-input v-model:value="formData.schoolCode" placeholder="请输入学校标准码" />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="主管部门" path="governingDepartment">
        <n-select
          v-model:value="formData.governingDepartment"
          placeholder="请选择或输入主管部门"
          :options="governingDepartmentOptions"
          filterable
          tag
        />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="所在地" path="location">
        <n-select
          v-model:value="formData.location"
          placeholder="请选择或输入所在地"
          :options="locationOptions"
          filterable
          tag
        />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="中央直属" path="isCentral">
        <n-select
          v-model:value="formData.isCentral"
          placeholder="请选择是否为中央直属"
          :options="booleanStatusOptions"
        />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="联系人姓名" path="contactName">
        <n-input v-model:value="formData.contactName" placeholder="请输入高校联系人姓名" />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="联系人部门" path="contactDepartment">
        <n-input v-model:value="formData.contactDepartment" placeholder="请输入高校联系人部门" />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="联系人职务" path="contactPosition">
        <n-input v-model:value="formData.contactPosition" placeholder="请输入高校联系人职务" />
      </n-form-item-grid-item>
      <n-form-item-grid-item label="联系方式" path="contactMethod">
        <n-input v-model:value="formData.contactMethod" placeholder="请输入联系人联系方式" />
      </n-form-item-grid-item>
      <n-form-item-grid-item :span="2" label="备注" path="remarks">
        <n-input
          v-model:value="formData.remarks"
          type="textarea"
          placeholder="请输入备注信息"
          :autosize="{ minRows: 3, maxRows: 5 }"
        />
      </n-form-item-grid-item>
    </n-grid>

    <div class="mt-6 flex justify-end space-x-4">
      <n-button @click="handleCancel">
        取消
      </n-button>
      <n-button type="primary" @click="handleSubmit">
        提交
      </n-button>
    </div>
  </n-form>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits, onMounted } from 'vue';
import {
  NForm,
  NFormItemGridItem,
  NInput,
  NSelect,
  NButton,
  NGrid,
  useMessage
} from 'naive-ui';
import { booleanStatusOptions, governingDepartmentOptions, locationOptions } from './universityOptions';

const props = defineProps({
  initialData: {
    type: Object,
    default: null,
  },
});

const emits = defineEmits(['submit', 'cancel']);

const message = useMessage();
const formRef = ref(null);
const defaultFormValues = {
  id: null,
  schoolName: '',
  schoolCode: '',
  governingDepartment: null,
  location: null,
  isCentral: null,
  contactDepartment: '',
  contactPosition: '',
  contactName: '',
  contactMethod: '',
  remarks: '',
};
const formData = ref({ ...defaultFormValues });

const rules = {
  schoolName: { required: true, message: '请输入学校名称', trigger: ['input', 'blur'] },
  schoolCode: { required: true, message: '请输入学校标准码', trigger: ['input', 'blur'] },
  isCentral: { type: 'boolean', required: true, message: '请选择是否为中央直属', trigger: ['change', 'blur'] },
  contactName: { required: true, message: '请输入联系人姓名', trigger: ['input', 'blur'] },
  contactMethod: { required: true, message: '请输入联系人联系方式', trigger: ['input', 'blur'] },
};

watch(() => props.initialData, (newData) => {
  if (newData) {
    formData.value = { ...defaultFormValues, ...newData };
  } else {
    formData.value = { ...defaultFormValues };
  }
}, { immediate: true, deep: true });

const handleSubmit = (e) => {
  e.preventDefault();
  formRef.value?.validate((errors) => {
    if (!errors) {
      emits('submit', { ...formData.value });
    } else {
      message.error('请检查表单填写是否正确');
    }
  });
};

const handleCancel = () => {
  emits('cancel');
};

const resetForm = () => {
  formData.value = { ...defaultFormValues };
  formRef.value?.restoreValidation();
};

defineExpose({
  resetForm,
});

onMounted(() => {
  if (props.initialData) {
    formData.value = { ...defaultFormValues, ...props.initialData };
  }
});
</script>