<template>
  <div class="px-4 sm:px-8 md:px-16 lg:px-48 pt-4 w-full">
    <div class="flex flex-col">
      <n-card :title="'高校管理'">
        <template #header-extra>
          <n-button :render-icon="renderAddIcon" type="primary" @click="openCreateModal">
            新增高校信息
          </n-button>
        </template>

        <div class="flex flex-row mt-4 space-x-4 items-center">
          <div>
            <n-input
              v-model:value="searchTerm"
              placeholder="按学校名称、代码、联系人搜索..."
              clearable
              @input="handleSearchDebounced"
              @clear="handleSearchDebounced"
            >
              <template #prefix>
                <n-icon :component="SearchOutline" />
              </template>
            </n-input>
          </div>
          <div class="min-w-48">
            <n-select
              v-model:value="filterIsCentral"
              placeholder="筛选是否中央直属"
              :options="page_config.isCentralOptions"
              clearable
              style="width: 100%"
              @update:value="handleFilterStateChange"
            />
          </div>
        </div>

        <div class="mt-6">
          <n-data-table
            :columns="columns"
            :data="paginatedUniversities"
            :loading="loading"
            :pagination="paginationConfig"
            :row-key="row => row.id"
            remote
            @update:page="handlePageChange"
            @update:sorter="handleSorterChange"
            @update:page-size="handlePageSizeChange"
            class="min-w-full"
            :scroll-x="2200"
          />
        </div>
      </n-card>
    </div>

    <n-modal
      v-model:show="showModal"
      preset="card"
      :style="{ width: '900px' }"
      :title="modalTitle"
      size="huge"
      :bordered="false"
      :segmented="{ content: 'soft', footer: 'soft' }"
      closable
      @after-leave="handleModalClose"
    >
      <university-form ref="universityFormRef" :initial-data="editingUniversity" @submit="handleFormSubmit" @cancel="closeModal" />
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h, computed, watch } from 'vue';
import {
  NButton, NInput, NIcon, NSelect, NDataTable, NModal, useMessage, NSpace, NTag, NPopconfirm
} from 'naive-ui';
import { SearchOutline, AddOutline as AddIcon, PencilOutline as EditIcon, TrashOutline as DeleteIcon } from '@vicons/ionicons5';
import UniversityForm from './UniversityForm.vue'; // 确保路径正确
import { booleanStatusOptions, governingDepartmentOptions, locationOptions } from './universityOptions'; // 确保路径正确
import { useUniversityStore } from '@/stores/universityStore'; // 引入Pinia Store
import { storeToRefs } from 'pinia';
// import { useDebounceFn } from '@vueuse/core'; // REMOVED: 用于搜索防抖

const message = useMessage();
const universityStore = useUniversityStore();

// 从 store 中获取响应式状态和 actions
const { rawUniversities, loading } = storeToRefs(universityStore);
const { fetchUniversities, addUniversity, updateUniversity, deleteUniversity } = universityStore;

// 组件本地 UI 状态
const searchTerm = ref('');
const filterIsCentral = ref(null);
const showModal = ref(false);
const modalTitle = ref('新增高校信息');
const editingUniversity = ref(null);
const universityFormRef = ref(null);

const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
});
const sorter = ref(null);

const page_config = reactive({
  isCentralOptions: [{label: '全部', value: null}, ...booleanStatusOptions],
});

// --- 计算属性，用于过滤、排序和分页 ---
const filteredAndSortedUniversities = computed(() => {
  let processedData = [...rawUniversities.value];

  if (searchTerm.value) {
    const term = searchTerm.value.toLowerCase();
    processedData = processedData.filter(item =>
      item.schoolName.toLowerCase().includes(term) ||
      item.schoolCode.toLowerCase().includes(term) ||
      (item.contactName && item.contactName.toLowerCase().includes(term))
    );
  }

  if (filterIsCentral.value !== null) {
    processedData = processedData.filter(item => item.isCentral === filterIsCentral.value);
  }

  if (sorter.value && sorter.value.order) {
    const { columnKey, order } = sorter.value;
    processedData.sort((a, b) => {
      let valA = a[columnKey];
      let valB = b[columnKey];
      if (typeof valA === 'string') valA = valA.toLowerCase();
      if (typeof valB === 'string') valB = valB.toLowerCase();
      if (valA < valB) return order === 'ascend' ? -1 : 1;
      if (valA > valB) return order === 'ascend' ? 1 : -1;
      return 0;
    });
  }
  return processedData;
});

const paginatedUniversities = computed(() => {
  const start = (pagination.page - 1) * pagination.pageSize;
  const end = start + pagination.pageSize;
  return filteredAndSortedUniversities.value.slice(start, end);
});

watch(filteredAndSortedUniversities, (newData) => {
  pagination.itemCount = newData.length;
  if (pagination.page > 1 && paginatedUniversities.value.length === 0) {
    pagination.page = Math.max(1, Math.ceil(newData.length / pagination.pageSize));
  }
}, { immediate: true });

const getOptionLabel = (value, options) => {
  const option = options.find(opt => opt.value === value);
  return option ? option.label : String(value);
};

const createColumns = ({ handleEdit, handleDeleteConfirmation }) => [
  { title: '序号', key: 'index', width: 60, render: (_, index) => (pagination.page - 1) * pagination.pageSize + index + 1 },
  { title: '学校名称', key: 'schoolName', width: 200, sorter: true, ellipsis: { tooltip: true } },
  { title: '学校标准码', key: 'schoolCode', width: 120, sorter: true },
  {
    title: '主管部门', key: 'governingDepartment', width: 180, sorter: true,
    render(row) { return getOptionLabel(row.governingDepartment, governingDepartmentOptions); },
    filterOptions: governingDepartmentOptions.map(opt => ({ label: opt.label, value: opt.value })),
    filter: (value, row) => row.governingDepartment === value,
  },
  {
    title: '所在地', key: 'location', width: 100, sorter: true,
    render(row) { return getOptionLabel(row.location, locationOptions); },
    filterOptions: locationOptions.map(opt => ({ label: opt.label, value: opt.value })),
    filter: (value, row) => row.location === value,
  },
  {
    title: '中央直属', key: 'isCentral', width: 100, sorter: true, align: 'center',
    render(row) {
      const statusLabel = getOptionLabel(row.isCentral, booleanStatusOptions);
      return h(NTag, { type: row.isCentral ? 'success' : 'default', bordered: false }, { default: () => statusLabel });
    },
    filterOptions: booleanStatusOptions.map(opt => ({label: opt.label, value: opt.value})),
    filter: (value, row) => row.isCentral === value
  },
  { title: '联系人', key: 'contactName', width: 100 },
  { title: '联系方式', key: 'contactMethod', width: 120 },
  { title: '备注', key: 'remarks', width: 200, ellipsis: { tooltip: true } },
  {
    title: '操作', key: 'actions', width: 150, align: 'center', fixed: 'right',
    render(row) {
      return h(NSpace, null, { default: () => [
        h(NButton, { size: 'small', type: 'primary', ghost: true, onClick: () => handleEdit(row), renderIcon: () => h(NIcon, null, { default: () => h(EditIcon) }) }, { default: () => '编辑' }),
        h(NPopconfirm, { onPositiveClick: () => handleDeleteConfirmation(row), positiveText: "确认删除", negativeText: "取消" },
          {
            trigger: () => h(NButton, { size: 'small', type: 'error', ghost: true, renderIcon: () => h(NIcon, null, { default: () => h(DeleteIcon) }) }, { default: () => '删除' }),
            default: () => `确定要删除高校 "${row.schoolName}" 吗？`
          }
        ),
      ]});
    }
  },
];

const columns = createColumns({
  handleEdit: (university) => {
    modalTitle.value = '编辑高校信息';
    editingUniversity.value = { ...university };
    showModal.value = true;
  },
  handleDeleteConfirmation: async (university) => {
    await deleteUniversity(university.id);
    message.success(`高校 "${university.schoolName}" 删除成功`);
    if (paginatedUniversities.value.length === 0 && pagination.page > 1) {
        pagination.page--;
    }
  },
});

const renderAddIcon = () => h(NIcon, null, { default: () => h(AddIcon) });
const openCreateModal = () => {
  modalTitle.value = '新增高校信息';
  editingUniversity.value = null;
  showModal.value = true;
};
const closeModal = () => { showModal.value = false; };
const handleModalClose = () => {
  editingUniversity.value = null;
  if (universityFormRef.value && typeof universityFormRef.value.resetForm === 'function') {
    universityFormRef.value.resetForm();
  }
};

const handleFormSubmit = async (formDataFromForm) => {
  if (editingUniversity.value && editingUniversity.value.id) {
    await updateUniversity({ ...editingUniversity.value, ...formDataFromForm });
    message.success(`高校 "${formDataFromForm.schoolName}" 更新成功`);
  } else {
    await addUniversity(formDataFromForm);
    message.success(`高校 "${formDataFromForm.schoolName}" 新增成功`);
  }
  closeModal();
};

const resetPageAndRefresh = () => {
  pagination.page = 1;
};

const handleSearch = () => resetPageAndRefresh();
// const handleSearchDebounced = useDebounceFn(handleSearch, 300); // REMOVED: 防抖
const handleFilterStateChange = () => resetPageAndRefresh();

const handlePageChange = (page) => { pagination.page = page; };
const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
};

const handleSorterChange = (newSorter) => {
  if (newSorter && newSorter.order) {
    sorter.value = { columnKey: newSorter.columnKey, order: newSorter.order };
  } else {
    sorter.value = null;
  }
  pagination.page = 1;
};

const paginationConfig = computed(() => ({
  page: pagination.page,
  pageSize: pagination.pageSize,
  itemCount: pagination.itemCount,
  showSizePicker: pagination.showSizePicker,
  pageSizes: pagination.pageSizes,
  onChange: handlePageChange,
  onUpdatePageSize: handlePageSizeChange,
  prefix({ itemCount }) {
    return `总共 ${itemCount} 条`;
  }
}));

onMounted(() => {
  fetchUniversities();
});
</script>