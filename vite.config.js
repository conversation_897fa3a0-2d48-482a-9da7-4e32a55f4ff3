import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path' // 添加这行导入

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  base: './',
  build: {
    outDir: 'dist', // 输出目录，要与 Electron main.js 中加载的路径一致

  },
  server: {
    port: 5173, // 开发服务器端口，要与 Electron main.js 中加载的 URL 一致
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  }
})