# 教育管理系统项目需求文档

## 项目概述

本项目是一个**干部授课管理系统**，用于管理中央单位干部在高校的授课活动。系统主要服务于教育培训管理部门，帮助他们统筹安排干部授课资源，优化教育培训效果。

## 技术栈

### 前端技术栈

- **框架**: Vue 3 (Composition API)
- **UI组件库**: Naive UI
- **状态管理**: Pinia
- **样式框架**: Tailwind CSS
- **图表库**: ECharts
- **路由**: Vue Router 4
- **构建工具**: Vite
- **开发语言**: JavaScript

### 后端技术栈（规划）

- **运行环境**: Node.js
- **框架**: Express.js
- **数据库**: MongoDB
- **开发语言**: JavaScript

## 系统核心实体

### 1. 授课干部 (Cadre)

**实体描述**: 来自各中央单位的干部，具备专业知识和授课能力

**核心属性**:

- `id`: 唯一标识符
- `name`: 姓名
- `gender`: 性别 (男/女/其他)
- `birthDate`: 出生年月 (时间戳)
- `nativePlace`: 籍贯
- `level`: 级别 (省级/厅局级/县处级/乡科级/其他)
- `position`: 职务
- `cadreType`: 干部类型 (厅局级干部/中管干部)
- `unitId`: 所属单位ID (关联中央单位)
- `highestEducation`: 最高学历 (博士研究生/硕士研究生/本科/大专/高中/其他)
- `highestDegree`: 最高学位 (博士/硕士/学士/无)
- `expertise`: 熟悉专长 (文本描述)
- `proposedTeachingDirections`: 拟授课方向 (数组，最多2个)
- `intendedUniversity1/2/3`: 意向授课高校1/2/3 (关联高校ID)
- `personnelChanges`: 人事变动情况
- `remarks`: 备注信息

**业务规则**:

- 每个干部必须归属于一个中央单位
- 拟授课方向基于习近平新时代中国特色社会主义思想的18个专题
- 可以设置最多3个意向授课高校
- 干部类型决定了授课级别和影响力

### 2. 中央单位 (Unit)

**实体描述**: 各级政府机关、事业单位等，是干部的工作单位

**核心属性**:

- `id`: 唯一标识符
- `name`: 单位全称
- `shortName`: 单位简称
- `calibrationCode`: 校准编号
- `receiptCode`: 收件编号
- `unitType`: 单位类型 (国家机关/事业单位/国有企业/社会团体/其他)
- `unitLevel`: 单位级别 (正部级/副部级/正局级/副局级/正处级/其他)
- `contactPerson`: 联系人
- `landlinePhone`: 座机号码
- `mobilePhone`: 手机号码
- `remarks`: 备注信息

**业务规则**:

- 单位名称和校准编号必须唯一
- 不同级别的单位对应不同级别的干部
- 联系信息用于授课安排的沟通协调

### 3. 授课高校 (University)

**实体描述**: 接受干部授课的高等院校

**核心属性**:

- `id`: 唯一标识符
- `schoolName`: 学校名称
- `schoolCode`: 学校标准码
- `governingDepartment`: 主管部门 (教育部/工信部/省教育厅/其他)
- `location`: 所在地 (北京/上海/广东/江苏/其他)
- `isCentral`: 是否中央直属 (布尔值)
- `contactDepartment`: 联系人部门
- `contactPosition`: 联系人职务
- `contactName`: 联系人姓名
- `contactMethod`: 联系方式
- `remarks`: 备注信息

**业务规则**:

- 学校名称和学校标准码必须唯一
- 中央直属高校在授课安排中具有优先级
- 联系信息用于授课具体安排的对接

### 4. 授课记录/计划 (Plan)

**实体描述**: 具体的授课安排记录，连接干部和高校

**核心属性**:

- `id`: 唯一标识符
- `cadreId`: 授课干部ID (关联干部)
- `universityId`: 授课高校ID (关联高校)
- `courseSeries`: 课程系列 (领导力与战略管理/公共政策与治理/经济发展与改革等)
- `courseName`: 课程名称 (基于干部的拟授课方向)
- `classDate`: 上课日期 (时间戳)
- `classTime`: 上课时间 (时间戳)
- `location`: 授课地点 (具体教室)
- `plannedHours`: 计划学时 (数值)
- `teachingStatus`: 授课状态 (计划中/进行中/已完成/已取消/已调整)
- `teachingSemester`: 授课学期 (2024-2025学年第一学期等)
- `remarks`: 备注信息

**业务规则**:

- 每条授课记录必须关联一个干部和一个高校
- 课程名称应该来自干部的拟授课方向
- 优先安排干部到其意向高校授课
- 授课状态反映计划的执行进度

## 实体关系

### 核心关系图

```
中央单位 (Unit) 1:N 授课干部 (Cadre)
授课干部 (Cadre) N:M 授课高校 (University) [通过意向关系]
授课干部 (Cadre) 1:N 授课记录 (Plan)
授课高校 (University) 1:N 授课记录 (Plan)
```

### 关系说明

1. **单位-干部关系**: 一对多关系，每个干部归属于一个单位，一个单位可以有多个干部
2. **干部-高校意向关系**: 多对多关系，干部可以设置多个意向高校，高校也可以被多个干部选择
3. **授课记录关系**: 授课记录是干部和高校的具体授课安排，连接两个实体

## 系统功能模块

### 1. 数据总览仪表板

- **统计数据展示**: 授课计划总数、干部总数、中央单位数、授课高校数等
- **本周授课信息**: 显示近期的授课安排，包括状态标识
- **数据分析图表**:
  - 月度授课量对比 (计划vs完成)
  - 季度计划完成率
  - 学期授课计划状态分布
  - 干部级别/学历/年龄结构分析
  - 热门授课方向统计
  - 单位类型分布
  - 高校地域分布和满意度

### 2. 干部管理模块

- **干部信息维护**: 新增、编辑、删除干部信息
- **专长和方向管理**: 管理干部的专业领域和拟授课方向
- **意向高校设置**: 设置干部的意向授课高校
- **人事变动跟踪**: 记录干部的人事变动情况

### 3. 单位管理模块

- **单位信息维护**: 管理中央单位的基本信息
- **联系信息管理**: 维护单位联系人和联系方式
- **单位分类管理**: 按类型和级别对单位进行分类

### 4. 高校管理模块

- **高校信息维护**: 管理授课高校的基本信息
- **联系信息管理**: 维护高校联系人和联系方式
- **高校分类管理**: 按主管部门和地域对高校进行分类

### 5. 授课计划管理模块

- **计划制定**: 创建新的授课计划，智能匹配干部和高校
- **计划调整**: 修改授课时间、地点等安排
- **状态跟踪**: 跟踪授课计划的执行状态
- **学期管理**: 按学期组织授课计划

## 业务流程

### 授课安排流程

1. **干部信息录入**: 录入干部基本信息、专长、拟授课方向和意向高校
2. **需求匹配**: 根据高校需求和干部专长进行匹配
3. **计划制定**: 创建具体的授课计划，包括时间、地点等
4. **计划确认**: 与干部和高校确认授课安排
5. **执行跟踪**: 跟踪授课计划的执行情况
6. **效果评估**: 收集授课效果反馈

### 数据管理流程

1. **基础数据维护**: 定期更新干部、单位、高校信息
2. **关系维护**: 维护干部与单位、干部与高校的关系
3. **状态更新**: 及时更新授课计划的执行状态
4. **数据分析**: 定期生成统计报告和分析图表

## 系统特色功能

### 1. 智能匹配

- 根据干部的拟授课方向和高校需求进行智能匹配
- 优先考虑干部的意向高校
- 考虑地域、时间等因素进行优化安排

### 2. 可视化分析

- 丰富的图表展示，包括饼图、柱状图、折线图等
- 多维度数据分析，支持按时间、地域、类型等维度查看
- 实时数据更新，反映最新的授课情况

### 3. 状态管理

- 完整的授课状态跟踪 (计划中→进行中→已完成)
- 支持计划调整和取消
- 历史记录保存，便于回溯和分析

### 4. 用户体验

- 响应式设计，支持多种设备访问
- 直观的操作界面，降低学习成本
- 丰富的交互反馈，提升用户体验

## 数据约束和验证

### 必填字段

- 干部: 姓名、性别、出生年月、单位归属、拟授课方向、意向高校1
- 单位: 单位名称、单位类型
- 高校: 学校名称、学校标准码、是否中央直属、联系人姓名、联系方式
- 授课计划: 授课干部、授课高校、拟授课方向、上课日期、上课时间、授课地点、计划学时、授课状态、授课学期

### 业务约束

- 干部的拟授课方向最多选择2个
- 干部最多设置3个意向高校
- 授课计划的学时必须大于0
- 同一干部在同一时间不能有多个授课安排

## 扩展性考虑

### 功能扩展

- 授课效果评估模块
- 干部培训记录管理
- 授课资源库管理
- 移动端应用支持

### 技术扩展

- 支持更多数据库类型
- 微服务架构改造
- 云部署支持
- API接口标准化

这个系统通过四个核心实体的有机结合，实现了干部授课资源的统一管理和优化配置，为教育培训工作提供了有力的信息化支撑。
